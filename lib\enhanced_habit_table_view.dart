import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:two_dimensional_scrollables/two_dimensional_scrollables.dart';
import 'habit.dart';
import 'section.dart';
import 'status_indicator.dart';
import 'database_service.dart';
import 'table_row_data.dart';
import 'modern_theme.dart';
import 'modern_habit_details_modal.dart';
import 'enhanced_entry_dialog.dart';
import 'quick_entry_components.dart';
import 'enhanced_score_components.dart';
import 'habit_details_screen.dart';
import 'habit_analytics_screen.dart';
import 'entry.dart';
import 'habit_analytics_service.dart';
import 'settings_service.dart';

class EnhancedHabitTableView extends StatefulWidget {
  final List<Habit> habits;
  final List<DateTime> dates;
  final List<Section> sections;
  final Function(int oldIndex, int newIndex)? onReorder;
  final bool showReorderDialog;
  final bool showPercentageRow;
  final VoidCallback? onDataChanged;
  final HabitAnalyticsService analyticsService;
  final SettingsService settingsService;

  const EnhancedHabitTableView({
    super.key,
    required this.habits,
    required this.dates,
    required this.sections,
    this.onReorder,
    this.showReorderDialog = true,
    this.showPercentageRow = true,
    this.onDataChanged,
    required this.analyticsService,
    required this.settingsService,
  });

  @override
  State<EnhancedHabitTableView> createState() => _EnhancedHabitTableViewState();
}

class _EnhancedHabitTableViewState extends State<EnhancedHabitTableView> {
  final _databaseService = DatabaseService();
  final List<TableRowData> _flatList = [];
  final ScrollController _verticalController = ScrollController();
  final ScrollController _horizontalController = ScrollController();

  // Add this helper method to calculate row height using TextPainter
  double _calculateRowHeight(String text, TextStyle style, double columnWidth) {
    final textPainter = TextPainter(
      text: TextSpan(text: text, style: style),
      maxLines: 2, // Allow up to two lines for wrapping
      textDirection: TextDirection.ltr,
    )..layout(maxWidth: columnWidth);

    // Add vertical padding to the calculated text height
    final calculatedHeight =
        textPainter.height + 32.0; // 16px top + 16px bottom padding

    // DEBUG: Log the calculated height for each habit
    debugPrint(
      'DEBUG RowHeight | Habit: $text | Calculated Height: $calculatedHeight',
    );

    return calculatedHeight;
  }

  @override
  void initState() {
    super.initState();
    _updateFlatList();
  }

  @override
  void didUpdateWidget(EnhancedHabitTableView oldWidget) {
    super.didUpdateWidget(oldWidget);
    // TASK 2: Ensure proper state propagation and rebuild when habits change
    if (oldWidget.habits != widget.habits ||
        oldWidget.dates != widget.dates ||
        oldWidget.sections != widget.sections) {
      debugPrint(
        '[ENHANCED_HABIT_TABLE_VIEW] Widget updated - rebuilding flat list',
      );
      debugPrint(
        '[ENHANCED_HABIT_TABLE_VIEW] Old habits: ${oldWidget.habits.length}, New habits: ${widget.habits.length}',
      );
      _updateFlatList();
      // Force rebuild to ensure percentage row updates
      setState(() {});
    }
  }

  @override
  void dispose() {
    _verticalController.dispose();
    _horizontalController.dispose();
    super.dispose();
  }

  void _updateFlatList() {
    _flatList.clear();
    for (int habitIndex = 0; habitIndex < widget.habits.length; habitIndex++) {
      final habit = widget.habits[habitIndex];
      Section? parentSection;
      if (habit.sectionIds.isNotEmpty && widget.sections.isNotEmpty) {
        parentSection = widget.sections.firstWhere(
          (section) => section.id == habit.sectionIds.first,
          orElse: () => Section(name: 'Unknown'),
        );
      } else {
        parentSection = Section(name: 'Unknown');
      }
      _flatList.add(HabitDataRow(habit, parentSection));
    }
  }

  // TASK 2: Enhanced percentage calculation with proper data source
  int _calculateCompletionPercentage(DateTime date) {
    if (widget.habits.isEmpty) return 0;

    int completedCount = 0;
    for (final habit in widget.habits) {
      // TASK 2: Use both new entry system and legacy completions for accuracy
      if (habit.isCompletedOnDate(date)) {
        completedCount++;
      }
    }

    debugPrint(
      '[ENHANCED_HABIT_TABLE_VIEW] Calculating percentage for ${date.day}/${date.month}: $completedCount/${widget.habits.length} = ${((completedCount / widget.habits.length) * 100).round()}%',
    );
    return ((completedCount / widget.habits.length) * 100).round();
  }

  // Smart Color Coding System
  Color getPercentageColor(int percentage, {required bool isDarkTheme}) {
    if (percentage == 0) {
      // Neutral gray for 0%
      return isDarkTheme ? const Color(0xFF6B7280) : const Color(0xFF9CA3AF);
    } else if (percentage <= 33) {
      // Tier 1: Low Completion (0-33%) - Red/Orange
      return isDarkTheme ? const Color(0xFFF87171) : const Color(0xFFEF4444);
    } else if (percentage <= 66) {
      // Tier 2: Moderate Completion (34-66%) - Amber/Yellow
      return isDarkTheme ? const Color(0xFFFBBF24) : const Color(0xFFF59E0B);
    } else if (percentage <= 89) {
      // Tier 3: Good Completion (67-89%) - Blue/Teal
      return isDarkTheme ? const Color(0xFF60A5FA) : const Color(0xFF3B82F6);
    } else {
      // Tier 4: Excellent Completion (90-100%) - Green
      return isDarkTheme ? const Color(0xFF34D399) : const Color(0xFF10B981);
    }
  }

  // Optimized layout calculation for better density
  ({double height, TextStyle style}) _calculateLayout(
    String text,
    BuildContext context,
  ) {
    final wordCount = text.split(' ').length;
    final baseStyle = Theme.of(context).textTheme.titleMedium!;

    // Optimized font sizes for better density
    double fontSize;
    if (wordCount > 6) {
      fontSize = 12.0; // Reduced for density
    } else if (wordCount > 3) {
      fontSize = 13.0; // Reduced for density
    } else {
      fontSize = 14.0; // Reduced for density
    }

    final dynamicTextStyle = baseStyle.copyWith(fontSize: fontSize);

    final textPainter = TextPainter(
      text: TextSpan(text: text, style: dynamicTextStyle),
      maxLines: 2,
      textDirection: TextDirection.ltr,
    )..layout(maxWidth: 130.0); // Slightly narrower for more columns

    // Optimized height for better density
    return (
      height: textPainter.height + 48.0, // Reduced padding
      style: dynamicTextStyle,
    );
  }

  @override
  Widget build(BuildContext context) {
    if (widget.habits.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.library_add_check_outlined,
              size: 60,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'No habits yet',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Add habits to get started',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return _buildOptimizedTable();
  }

  Widget _buildOptimizedTable() {
    // Calculate all row heights using the new dynamic system
    final habitColumnWidth = 130.0; // The fixed width of the habit name column
    final textStyle = GoogleFonts.inter(
      fontSize: 14,
      fontWeight: FontWeight.w500,
    );

    // Start with fixed heights for the header rows
    final Map<int, TableSpanExtent> rowHeights = {
      0: const FixedTableSpanExtent(32), // Percentage Header
      1: const FixedTableSpanExtent(48), // Date Header
    };

    // Calculate the height for each habit row using the new helper method
    for (int i = 0; i < widget.habits.length; i++) {
      final habit = widget.habits[i];
      final calculatedHeight = _calculateRowHeight(
        habit.name,
        textStyle,
        habitColumnWidth,
      );
      rowHeights[i + 2] = FixedTableSpanExtent(calculatedHeight);
    }

    // Keep the existing text style calculation for backward compatibility
    final Map<String, double> calculatedRowHeights = {};
    final Map<String, TextStyle> calculatedTextStyles = {};

    for (final habit in widget.habits) {
      final layout = _calculateLayout(habit.name, context);
      calculatedRowHeights[habit.id] = layout.height;
      calculatedTextStyles[habit.id] = layout.style;
    }

    return TableView.builder(
      // Enable internal horizontal scrolling
      horizontalDetails: ScrollableDetails.horizontal(
        controller: _horizontalController,
        physics: const AlwaysScrollableScrollPhysics(),
      ),
      // Disable vertical scrolling to avoid conflicts
      verticalDetails: ScrollableDetails.vertical(
        controller: _verticalController,
        physics: const NeverScrollableScrollPhysics(),
      ),
      columnCount: widget.dates.length + 1,
      rowCount: _flatList.length + (widget.showPercentageRow ? 2 : 1),
      pinnedRowCount: widget.showPercentageRow ? 2 : 1,
      pinnedColumnCount: 1,
      columnBuilder: _buildColumnSpan,
      rowBuilder: (int index) {
        if (rowHeights.containsKey(index)) {
          return TableSpan(extent: rowHeights[index]!);
        } else {
          return const TableSpan(
            extent: FixedTableSpanExtent(52),
          ); // Reduced default
        }
      },
      cellBuilder: (context, vicinity) {
        return _buildCellFlattened(context, vicinity, calculatedTextStyles);
      },
    );
  }

  TableSpan _buildColumnSpan(int index) {
    if (index == 0) {
      // Habit name column with a fixed width
      return const TableSpan(extent: FixedTableSpanExtent(150));
    } else {
      // Date columns with a fixed width
      return const TableSpan(extent: FixedTableSpanExtent(60));
    }
  }

  TableViewCell _buildCellFlattened(
    BuildContext context,
    TableVicinity vicinity,
    Map<String, TextStyle> calculatedTextStyles,
  ) {
    final rowIndex = vicinity.row;
    final columnIndex = vicinity.column;

    if (widget.showPercentageRow) {
      final flatListIndex = rowIndex - 2;

      if (rowIndex == 0) {
        return _buildEnhancedPercentageCell(columnIndex);
      }
      if (rowIndex == 1) {
        return _buildDateHeaderCell(columnIndex);
      }

      if (flatListIndex >= 0 && flatListIndex < _flatList.length) {
        final rowData = _flatList[flatListIndex];
        return _buildHabitRow(rowData, columnIndex, calculatedTextStyles);
      }
    } else {
      final flatListIndex = rowIndex - 1;

      if (rowIndex == 0) {
        return _buildDateHeaderCell(columnIndex);
      }

      if (flatListIndex >= 0 && flatListIndex < _flatList.length) {
        final rowData = _flatList[flatListIndex];
        return _buildHabitRow(rowData, columnIndex, calculatedTextStyles);
      }
    }

    return _buildErrorCell('Invalid cell');
  }

  TableViewCell _buildEnhancedPercentageCell(int columnIndex) {
    final theme = Theme.of(context);
    final bool isDarkMode = theme.brightness == Brightness.dark;
    final bool isDarkTheme = theme.brightness == Brightness.dark;

    debugPrint('DEBUG PercentageCell | isDarkTheme: $isDarkTheme');

    // Use a specific, visible grey for dark mode borders
    final Color borderColor = isDarkMode
        ? Colors.grey.shade800
        : theme.dividerColor;

    if (columnIndex == 0) {
      return TableViewCell(
        child: Container(
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceContainerHighest,
            border: Border(
              right: BorderSide(color: borderColor, width: 1),
              bottom: BorderSide(color: borderColor, width: 1),
            ),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
          child: Center(
            child: Text(
              '%',
              style: GoogleFonts.inter(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color:
                    theme.textTheme.labelSmall?.color ??
                    const Color(0xFF9E9E9E),
              ),
            ),
          ),
        ),
      );
    } else {
      final dateIndex = columnIndex - 1;
      if (dateIndex >= 0 && dateIndex < widget.dates.length) {
        final date = widget.dates[dateIndex];
        final percentage = _calculateCompletionPercentage(date);
        final percentageColor = getPercentageColor(
          percentage,
          isDarkTheme: isDarkTheme,
        );

        return TableViewCell(
          child: Container(
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceContainerHighest,
              border: Border(
                right: BorderSide(color: borderColor, width: 1),
                bottom: BorderSide(color: borderColor, width: 1),
              ),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 2.0, vertical: 4.0),
            child: Text(
              '$percentage%',
              style: GoogleFonts.inter(
                fontSize: 10,
                fontWeight: FontWeight.w600,
                color: percentageColor, // Smart color coding applied
              ),
              textAlign: TextAlign.center,
            ),
          ),
        );
      }
    }

    return _buildErrorCell('Invalid %');
  }

  TableViewCell _buildDateHeaderCell(int columnIndex) {
    final theme = Theme.of(context);
    final bool isDarkMode = theme.brightness == Brightness.dark;

    // Use a specific, visible grey for dark mode borders
    final Color borderColor = isDarkMode
        ? Colors.grey.shade800
        : theme.dividerColor;

    if (columnIndex == 0) {
      return TableViewCell(
        child: Container(
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            border: Border(
              right: BorderSide(color: borderColor, width: 1),
              bottom: BorderSide(color: borderColor, width: 1),
            ),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 6.0),
          child: Center(
            child: Text(
              'Habit',
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color:
                    theme.textTheme.titleMedium?.color ??
                    const Color(0xFFE0E0E0),
              ),
            ),
          ),
        ),
      );
    } else {
      final dateIndex = columnIndex - 1;
      if (dateIndex >= 0 && dateIndex < widget.dates.length) {
        final date = widget.dates[dateIndex];
        final isToday = _isSameDay(date, DateTime.now());

        return TableViewCell(
          child: Container(
            decoration: BoxDecoration(
              color: isToday
                  ? theme.primaryColor.withOpacity(0.1)
                  : theme.colorScheme.surface,
              border: Border(
                right: BorderSide(color: borderColor, width: 1),
                bottom: BorderSide(color: borderColor, width: 1),
              ),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 2.0, vertical: 4.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '${date.day}',
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    fontWeight: isToday ? FontWeight.w600 : FontWeight.w500,
                    color: isToday
                        ? theme.primaryColor
                        : theme.colorScheme.onSurface,
                  ),
                  textAlign: TextAlign.center,
                ),
                Text(
                  _getWeekdayAbbr(date.weekday),
                  style: GoogleFonts.inter(
                    fontSize: 9,
                    fontWeight: FontWeight.w400,
                    color:
                        theme.textTheme.labelSmall?.color ??
                        const Color(0xFF9E9E9E),
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      }
    }

    return _buildErrorCell('Invalid date');
  }

  TableViewCell _buildHabitRow(
    TableRowData rowData,
    int columnIndex,
    Map<String, TextStyle> calculatedTextStyles,
  ) {
    switch (rowData) {
      case HabitDataRow habitRow:
        if (columnIndex == 0) {
          return _buildHabitNameCell(habitRow.habit, calculatedTextStyles);
        } else {
          final dateIndex = columnIndex - 1;
          if (dateIndex >= 0 && dateIndex < widget.dates.length) {
            return _buildStatusCell(habitRow.habit, widget.dates[dateIndex]);
          }
        }
        break;
      case SectionHeaderRow sectionRow:
        if (columnIndex == 0) {
          return TableViewCell(
            child: _buildSectionHeaderCell(sectionRow.section),
          );
        }
        break;
    }
    return _buildErrorCell('Invalid habit row');
  }

  TableViewCell _buildHabitNameCell(
    Habit habit,
    Map<String, TextStyle> calculatedTextStyles,
  ) {
    final theme = Theme.of(context);
    final bool isDarkMode = theme.brightness == Brightness.dark;

    // Use a specific, visible grey for dark mode borders
    final Color borderColor = isDarkMode
        ? Colors.grey.shade800
        : theme.dividerColor;

    debugPrint(
      'DEBUG Border Color | isDarkMode: $isDarkMode, borderColor: $borderColor',
    );

    return TableViewCell(
      child: GestureDetector(
        onLongPress: () => _showHabitManagementDialog(habit),
        onTap: () => _navigateToAnalytics(habit),
        child: Container(
          decoration: BoxDecoration(
            color: theme.cardColor,
            border: Border(
              right: BorderSide(color: borderColor, width: 1),
              bottom: BorderSide(color: borderColor, width: 1),
            ),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
          // TASK 4: Simplified habit name cell with only name and streak
          child: Row(
            children: [
              // Section color indicator
              Container(
                width: 4,
                height: 16,
                decoration: BoxDecoration(
                  color: _getHabitColor(habit),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(width: 8),

              // Habit name - Enable text wrapping without maxLines restriction
              Expanded(
                child: Text(
                  habit.name,
                  style:
                      calculatedTextStyles[habit.id]?.copyWith(
                        color: Theme.of(context).textTheme.titleMedium?.color,
                      ) ??
                      GoogleFonts.inter(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color:
                            Theme.of(context).textTheme.titleMedium?.color ??
                            const Color(0xFFE0E0E0),
                      ),
                  softWrap: true,
                  maxLines: 2, // Allow up to 2 lines for wrapping
                  overflow: TextOverflow
                      .visible, // Changed from ellipsis to visible to allow wrapping
                ),
              ),

              const SizedBox(width: 8),

              // TASK 4: Streak count (discreetly styled)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: _getHabitColor(habit).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  '${habit.currentStreak}',
                  style: GoogleFonts.inter(
                    fontSize: 11,
                    fontWeight: FontWeight.w600,
                    color: _getHabitColor(habit),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  TableViewCell _buildStatusCell(Habit habit, DateTime date) {
    final theme = Theme.of(context);
    final bool isDarkMode = theme.brightness == Brightness.dark;

    // Use a specific, visible grey for dark mode borders
    final Color borderColor = isDarkMode
        ? Colors.grey.shade800
        : theme.dividerColor;

    final isToday = _isSameDay(date, DateTime.now());
    final isFuture = date.isAfter(DateTime.now());

    // Check both new entry system and legacy completions
    final entry = habit.getEntryForDate(date);
    bool isCompleted = habit.isCompletedOnDate(date);

    HabitStatus status;
    if (isFuture) {
      status = HabitStatus.pending;
    } else {
      status = isCompleted ? HabitStatus.completed : HabitStatus.missed;
    }

    return TableViewCell(
      child: Container(
        decoration: BoxDecoration(
          color: theme.cardColor,
          border: Border(
            right: BorderSide(color: borderColor, width: 1),
            bottom: BorderSide(color: borderColor, width: 1),
          ),
        ),
        padding: EdgeInsets.all(ModernTheme.spaceXS), // 4px padding
        child: Center(
          child: InkWell(
            onTap: isFuture ? null : () => _handleCellTap(habit, date),
            onLongPress: isFuture
                ? null
                : () => _showDetailedEntryDialog(habit, date),
            borderRadius: BorderRadius.circular(8),
            child: Container(
              padding: EdgeInsets.all(ModernTheme.spaceXXS), // 2px padding
              child: _buildCellContent(habit, date, status, isToday, entry),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeaderCell(Section section) {
    final theme = Theme.of(context);
    final bool isDarkMode = theme.brightness == Brightness.dark;

    // Use a specific, visible grey for dark mode borders
    final Color borderColor = isDarkMode
        ? Colors.grey.shade800
        : theme.dividerColor;

    return Container(
      decoration: BoxDecoration(
        color: theme.primaryColor,
        border: Border(
          right: BorderSide(color: borderColor, width: 1),
          bottom: BorderSide(color: borderColor, width: 1),
        ),
      ),
      padding: EdgeInsets.symmetric(
        horizontal: ModernTheme.spaceSM * 0.9,
        vertical: ModernTheme.spaceXS * 1.1,
      ), // 25% reduction: 7.5px horizontal, 4.5px vertical
      child: Text(
        section.name,
        style: GoogleFonts.inter(
          fontSize: 13,
          fontWeight: FontWeight.w600,
          color: Theme.of(context).colorScheme.onPrimary,
        ),
      ),
    );
  }

  TableViewCell _buildErrorCell(String message) {
    final theme = Theme.of(context);
    final bool isDarkMode = theme.brightness == Brightness.dark;

    // Use a specific, visible grey for dark mode borders
    final Color borderColor = isDarkMode
        ? Colors.grey.shade800
        : theme.dividerColor;

    return TableViewCell(
      child: Container(
        decoration: BoxDecoration(
          color: Colors.red,
          border: Border(
            right: BorderSide(color: borderColor, width: 1),
            bottom: BorderSide(color: borderColor, width: 1),
          ),
        ),
        padding: EdgeInsets.all(
          ModernTheme.spaceXS * 1.1,
        ), // 25% reduction: 4.5px
        child: Center(
          child: Text(
            message,
            style: const TextStyle(color: Colors.white, fontSize: 9),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  // Enhanced cell content builder
  Widget _buildCellContent(
    Habit habit,
    DateTime date,
    HabitStatus status,
    bool isToday,
    Entry? entry,
  ) {
    if (habit.type == HabitType.numerical && entry != null) {
      // Show numerical value for numerical habits
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          StatusIndicator(status: status, isToday: isToday),
          if (entry.numericalValue != null && entry.numericalValue! > 0) ...[
            const SizedBox(height: 2),
            Text(
              '${entry.numericalValue!.toStringAsFixed(entry.numericalValue! % 1 == 0 ? 0 : 1)}',
              style: GoogleFonts.inter(
                fontSize: 8,
                fontWeight: FontWeight.w500,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              ),
            ),
          ],
        ],
      );
    } else {
      // Standard status indicator for boolean habits
      return StatusIndicator(status: status, isToday: isToday);
    }
  }

  // Enhanced cell tap handler
  Future<void> _handleCellTap(Habit habit, DateTime date) async {
    if (habit.type == HabitType.boolean) {
      // Quick toggle for boolean habits
      await _toggleHabitCompletion(habit, date);
    } else {
      // Show quick numerical entry dialog
      await _showQuickNumericalEntry(habit, date);
    }
  }

  // COMPREHENSIVE DEBUGGING: Navigate to analytics screen
  Future<void> _navigateToAnalytics(Habit habit) async {
    try {
      debugPrint('[ENHANCED_HABIT_TABLE_VIEW] === NAVIGATION TO ANALYTICS ===');
      debugPrint(
        '[ENHANCED_HABIT_TABLE_VIEW] Navigating to analytics for habit: ${habit.name} (ID: ${habit.id})',
      );
      debugPrint('[ENHANCED_HABIT_TABLE_VIEW] Habit data: ${habit.toJson()}');

      await Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) {
            debugPrint(
              '[ENHANCED_HABIT_TABLE_VIEW] Building HabitAnalyticsScreen widget',
            );
            return HabitAnalyticsScreen(habit: habit);
          },
        ),
      );

      debugPrint('[ENHANCED_HABIT_TABLE_VIEW] Returned from analytics screen');

      // Refresh data when returning
      if (widget.onDataChanged != null) {
        debugPrint(
          '[ENHANCED_HABIT_TABLE_VIEW] Calling onDataChanged callback',
        );
        widget.onDataChanged!();
        debugPrint(
          '[ENHANCED_HABIT_TABLE_VIEW] onDataChanged callback completed',
        );
      }

      debugPrint('[ENHANCED_HABIT_TABLE_VIEW] === NAVIGATION COMPLETE ===');
    } catch (e, stackTrace) {
      debugPrint(
        '[ENHANCED_HABIT_TABLE_VIEW] ERROR: Failed to navigate to analytics - $e',
      );
      debugPrint('[ENHANCED_HABIT_TABLE_VIEW] StackTrace: $stackTrace');

      // Show error to user
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to open analytics: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Show quick numerical entry dialog
  Future<void> _showQuickNumericalEntry(Habit habit, DateTime date) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => QuickNumericalEntryDialog(
        habit: habit,
        date: date,
        existingEntry: habit.getEntryForDate(date),
      ),
    );

    if (result == true && widget.onDataChanged != null) {
      widget.onDataChanged!();
    }
  }

  // Show detailed entry dialog
  Future<void> _showDetailedEntryDialog(Habit habit, DateTime date) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => EnhancedEntryDialog(
        habit: habit,
        date: date,
        existingEntry: habit.getEntryForDate(date),
      ),
    );

    if (result == true && widget.onDataChanged != null) {
      widget.onDataChanged!();
    }
  }

  // Enhanced toggle method for boolean habits
  Future<void> _toggleHabitCompletion(Habit habit, DateTime date) async {
    try {
      HapticFeedback.lightImpact();

      if (habit.type == HabitType.boolean) {
        // Use new entry system for boolean habits
        final existingEntry = habit.getEntryForDate(date);
        final newValue = !(existingEntry?.boolValue ?? false);

        final entry = Entry(
          id: existingEntry?.id,
          habitId: habit.id,
          timestamp: date,
          value: newValue,
          note: existingEntry?.note,
          type: EntryType.boolean,
        );

        // COMPREHENSIVE DEBUGGING: Save entry and habit
        debugPrint(
          '[ENHANCED_HABIT_TABLE_VIEW] Saving entry and updating habit',
        );
        debugPrint(
          '[ENHANCED_HABIT_TABLE_VIEW] Habit: ${habit.name} (ID: ${habit.id})',
        );
        debugPrint('[ENHANCED_HABIT_TABLE_VIEW] Entry: ${entry.toJson()}');
        await _databaseService.saveEntry(entry);
        habit.addEntry(entry);
        await _databaseService.saveHabit(habit);
        debugPrint(
          '[ENHANCED_HABIT_TABLE_VIEW] Entry and habit saved successfully',
        );
      } else {
        // Fallback to legacy system for backward compatibility
        final currentStatus = habit.completions[date] ?? false;
        final Map<DateTime, bool> updatedCompletions = Map<DateTime, bool>.from(
          habit.completions,
        );

        if (currentStatus) {
          updatedCompletions.remove(date);
        } else {
          updatedCompletions[date] = true;
        }

        final updatedHabit = habit.copyWith(completions: updatedCompletions);
        await _databaseService.updateHabit(updatedHabit);
      }

      if (widget.onDataChanged != null) {
        widget.onDataChanged!();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update habit: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _showHabitManagementDialog(Habit habit) async {
    HapticFeedback.mediumImpact();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Manage "${habit.name}"'),
        content: const Text('What would you like to do with this habit?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          if (widget.showReorderDialog && widget.onReorder != null)
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _showReorderHabitsDialog();
              },
              child: const Text('Reorder'),
            ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showHabitDetailsDialog(habit);
            },
            child: const Text('Details'),
          ),
        ],
      ),
    );
  }

  Future<void> _showHabitDetailsDialog(Habit habit) async {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) =>
          ModernHabitDetailsModal(habit: habit, sections: widget.sections),
    );
  }

  Future<void> _showReorderHabitsDialog() async {
    if (widget.habits.length < 2) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Need at least 2 habits to reorder'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    if (widget.onReorder == null) return;

    List<Habit> reorderableHabits = List.from(widget.habits);

    await showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Text('Reorder Habits'),
          content: SizedBox(
            width: double.maxFinite,
            height: 400,
            child: ReorderableListView.builder(
              itemCount: reorderableHabits.length,
              onReorder: (oldIndex, newIndex) {
                setDialogState(() {
                  if (newIndex > oldIndex) {
                    newIndex -= 1;
                  }
                  final habit = reorderableHabits.removeAt(oldIndex);
                  reorderableHabits.insert(newIndex, habit);
                });
              },
              itemBuilder: (context, index) {
                final habit = reorderableHabits[index];
                return Card(
                  key: ValueKey(habit.id),
                  margin: const EdgeInsets.symmetric(vertical: 2),
                  child: ListTile(
                    leading: Container(
                      width: 4,
                      height: 20,
                      decoration: BoxDecoration(
                        color: _getHabitColor(habit),
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    title: Text(
                      habit.name,
                      style: const TextStyle(fontSize: 16),
                    ),
                    subtitle: Text('Section: ${_getSectionNames(habit)}'),
                    trailing: const Icon(Icons.drag_handle),
                  ),
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                for (int i = 0; i < reorderableHabits.length; i++) {
                  final originalIndex = widget.habits.indexWhere(
                    (h) => h.id == reorderableHabits[i].id,
                  );
                  if (originalIndex != i && widget.onReorder != null) {
                    widget.onReorder!(originalIndex, i);
                  }
                }

                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Habit order updated successfully'),
                    backgroundColor: Colors.green,
                  ),
                );
              },
              child: const Text('Save Order'),
            ),
          ],
        ),
      ),
    );
  }

  // Helper methods
  Color _getHabitColor(Habit habit) {
    // Get color from section assignment
    if (habit.sectionIds.isNotEmpty && widget.sections.isNotEmpty) {
      final section = widget.sections.firstWhere(
        (s) => s.id == habit.sectionIds.first,
        orElse: () => widget.sections.first,
      );

      // Convert section color hex to Color object
      return _hexToColor(section.color);
    }

    // Fallback to default blue if no section
    return const Color(0xFF3B82F6);
  }

  // Convert hex string to Color object
  Color _hexToColor(String hex) {
    try {
      // Remove # if present
      final cleanHex = hex.replaceAll('#', '');
      final colorValue = int.parse(cleanHex, radix: 16);
      return Color(0xFF000000 + colorValue);
    } catch (e) {
      // Fallback to blue if parsing fails
      return const Color(0xFF3B82F6);
    }
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  String _getWeekdayAbbr(int weekday) {
    const weekdays = ['MO', 'TU', 'WE', 'TH', 'FR', 'SA', 'SU'];
    return weekdays[weekday - 1];
  }

  String _getSectionNames(Habit habit) {
    if (habit.sectionIds.isEmpty) return 'No sections';

    final sectionNames = habit.sectionIds.map((sectionId) {
      final section = widget.sections.firstWhere(
        (s) => s.id == sectionId,
        orElse: () => Section(name: 'Unknown'),
      );
      return section.name;
    }).toList();

    return sectionNames.join(', ');
  }
}
