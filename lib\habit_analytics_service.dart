import 'dart:math' as math;
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'habit.dart';
import 'entry.dart';
import 'settings_service.dart';

/// Time scales for analytics charts
enum TimeScale {
  day,
  week,
  month,
  quarter,
  year,
}

/// Data point for charts
class ChartDataPoint {
  final DateTime date;
  final double value;
  final String label;
  final bool isFuture; // Add this new field
  final double xValue; // Add xValue for fixed 14-point layout

  ChartDataPoint({
    required this.date,
    required this.value,
    required this.label,
    this.isFuture = false, // Default to false
    required this.xValue, // Required for new paged design
  });
}

/// Service for calculating habit analytics and providing data for charts
class HabitAnalyticsService {
  final Habit habit;
  final List<Entry> entries;

  HabitAnalyticsService({
    required this.habit,
    required this.entries,
  });

  /// Calculate the current week number based on user's start of week preference
  /// Returns a formatted string like "W28"
  static Future<String> getCurrentWeekNumber(SettingsService settingsService) async {
    final now = DateTime.now();
    final startOfWeekDay = await settingsService.getStartOfWeek();
    
    // Calculate the week number based on the user's start day preference
    int weekNumber;
    
    if (startOfWeekDay == SettingsService.SUNDAY) {
      // Sunday-based week calculation
      final startOfYear = DateTime(now.year, 1, 1);
      final firstSunday = startOfYear.subtract(Duration(days: startOfYear.weekday % 7));
      final daysSinceFirstSunday = now.difference(firstSunday).inDays;
      weekNumber = (daysSinceFirstSunday / 7).floor() + 1;
    } else {
      // Monday-based week calculation (ISO 8601)
      final startOfYear = DateTime(now.year, 1, 1);
      final firstMonday = startOfYear.subtract(Duration(days: (startOfYear.weekday - 1) % 7));
      final daysSinceFirstMonday = now.difference(firstMonday).inDays;
      weekNumber = (daysSinceFirstMonday / 7).floor() + 1;
    }
    
    // Ensure week number is within valid range (1-53)
    weekNumber = weekNumber.clamp(1, 53);
    
    final startDayName = startOfWeekDay == SettingsService.SUNDAY ? 'Sunday' : 'Monday';
    developer.log('DEBUG WeekCalc | Start Day: $startDayName | Calculated Week: W$weekNumber');
    
    return 'W$weekNumber';
  }

  /// Calculate habit strength score using the precise formula from habit_metrics.md
  /// score = previousScore × multiplier + checkmarkValue × (1 − multiplier)
  /// NOTE: This method is renamed from calculateScore to avoid confusion with percentage calculations
  /// For simple completion percentages, use calculateCompletionPercentage() instead
  double calculateHabitStrengthScore(TimeScale timeScale) {
    final now = DateTime.now();
    
    if (timeScale == TimeScale.week) {
      // PHASE 2: For week calculations, use legacy logic here but recommend using calculateThisWeekPercentage()
      // This maintains compatibility while the new async method provides accurate calendar week calculations
      final currentWeekday = now.weekday; // 1 = Monday, 7 = Sunday
      final daysFromMonday = currentWeekday - 1;
      final effectiveStartDate = DateTime(now.year, now.month, now.day - daysFromMonday);
      final effectiveEndDate = DateTime(now.year, now.month, now.day + 1);
      
      debugPrint('[HABIT_ANALYTICS] Legacy week boundaries: ${effectiveStartDate} to ${effectiveEndDate}');
      
      // Filter entries within the time range
      final relevantEntries = entries.where((entry) {
        final entryDate = DateTime(entry.timestamp.year, entry.timestamp.month, entry.timestamp.day);
        return (entryDate.isAfter(effectiveStartDate) || entryDate.isAtSameMomentAs(effectiveStartDate)) && 
               entryDate.isBefore(effectiveEndDate);
      }).toList();

      if (relevantEntries.isEmpty) return 0.0;

      // Sort entries by date
      relevantEntries.sort((a, b) => a.timestamp.compareTo(b.timestamp));

      double score = 0.0;
      final multiplier = _getMultiplierForHabitFrequency();

      for (final entry in relevantEntries) {
        final checkmarkValue = _getCheckmarkValue(entry);
        score = score * multiplier + checkmarkValue * (1 - multiplier);
      }

      return score.clamp(0.0, 1.0);
    } else {
      // For non-week timescales, use original logic
      final startDate = _getStartDateForTimeScale(now, timeScale);
      final effectiveEndDate = now.add(const Duration(days: 1));
      
      final relevantEntries = entries.where((entry) {
        return entry.timestamp.isAfter(startDate) && 
               entry.timestamp.isBefore(effectiveEndDate);
      }).toList();

      if (relevantEntries.isEmpty) return 0.0;

      relevantEntries.sort((a, b) => a.timestamp.compareTo(b.timestamp));

      double score = 0.0;
      final multiplier = _getMultiplierForHabitFrequency();

      for (final entry in relevantEntries) {
        final checkmarkValue = _getCheckmarkValue(entry);
        score = score * multiplier + checkmarkValue * (1 - multiplier);
      }

      return score.clamp(0.0, 1.0);
    }
  }

  /// Get multiplier based on habit frequency (daily habits have different multiplier than weekly)
  double _getMultiplierForHabitFrequency() {
    // For daily habits, use 0.95 (retains 95% of previous score)
    // This can be customized based on habit frequency settings
    return 0.95;
  }

  /// Convert entry to checkmark value (0.0 or 1.0 for boolean, percentage for numerical)
  double _getCheckmarkValue(Entry entry) {
    if (entry.type == EntryType.boolean) {
      return entry.boolValue ? 1.0 : 0.0;
    } else if (entry.type == EntryType.numerical && habit.targetValue != null) {
      final value = entry.numericalValue ?? 0.0;
      return (value / habit.targetValue!).clamp(0.0, 1.0);
    }
    return 0.0;
  }

  /// Calculate current streak with start and end dates
  Map<String, dynamic> calculateCurrentStreakWithDates() {
    if (entries.isEmpty) return {'length': 0, 'startDate': null, 'endDate': null};

    final sortedEntries = List<Entry>.from(entries);
    sortedEntries.sort((a, b) => b.timestamp.compareTo(a.timestamp)); // Most recent first

    int streak = 0;
    DateTime currentDate = DateTime.now();
    DateTime? streakStartDate;
    DateTime? streakEndDate;
    
    for (int i = 0; i < 365; i++) { // Check up to a year back
      final dateToCheck = DateTime(currentDate.year, currentDate.month, currentDate.day);
      final entryForDate = sortedEntries.where((entry) {
        final entryDate = DateTime(entry.timestamp.year, entry.timestamp.month, entry.timestamp.day);
        return entryDate == dateToCheck;
      }).firstOrNull;

      if (entryForDate != null && _isEntryCompleted(entryForDate)) {
        streak++;
        if (streakEndDate == null) {
          streakEndDate = dateToCheck; // First completed day (most recent)
        }
        streakStartDate = dateToCheck; // Keep updating to get the earliest day
      } else {
        break;
      }

      currentDate = currentDate.subtract(const Duration(days: 1));
    }

    return {
      'length': streak,
      'startDate': streakStartDate,
      'endDate': streakEndDate,
    };
  }

  /// Calculate current streak (backward compatibility)
  int calculateCurrentStreak() {
    final result = calculateCurrentStreakWithDates();
    return result['length'] as int;
  }

  /// Calculate best streak with start and end dates
  Map<String, dynamic> calculateBestStreakWithDates() {
    if (entries.isEmpty) return {'length': 0, 'startDate': null, 'endDate': null};

    final sortedEntries = List<Entry>.from(entries);
    sortedEntries.sort((a, b) => a.timestamp.compareTo(b.timestamp));

    int bestStreak = 0;
    int currentStreak = 0;
    DateTime? lastDate;
    DateTime? bestStreakStartDate;
    DateTime? bestStreakEndDate;
    DateTime? currentStreakStartDate;

    for (final entry in sortedEntries) {
      if (_isEntryCompleted(entry)) {
        final entryDate = DateTime(entry.timestamp.year, entry.timestamp.month, entry.timestamp.day);
        
        if (lastDate == null || entryDate.difference(lastDate).inDays == 1) {
          if (currentStreak == 0) {
            currentStreakStartDate = entryDate;
          }
          currentStreak++;
          
          if (currentStreak > bestStreak) {
            bestStreak = currentStreak;
            bestStreakStartDate = currentStreakStartDate;
            bestStreakEndDate = entryDate;
          }
        } else if (entryDate.difference(lastDate!).inDays > 1) {
          currentStreak = 1;
          currentStreakStartDate = entryDate;
          
          if (currentStreak > bestStreak) {
            bestStreak = currentStreak;
            bestStreakStartDate = currentStreakStartDate;
            bestStreakEndDate = entryDate;
          }
        }
        
        lastDate = entryDate;
      } else {
        currentStreak = 0;
        currentStreakStartDate = null;
      }
    }

    return {
      'length': bestStreak,
      'startDate': bestStreakStartDate,
      'endDate': bestStreakEndDate,
    };
  }

  /// Calculate best streak (backward compatibility)
  int calculateBestStreak() {
    final result = calculateBestStreakWithDates();
    return result['length'] as int;
  }

  /// Check if an entry represents a completion
  bool _isEntryCompleted(Entry entry) {
    if (entry.type == EntryType.boolean) {
      return entry.boolValue;
    } else if (entry.type == EntryType.numerical && habit.targetValue != null) {
      final value = entry.numericalValue ?? 0.0;
      return value >= (habit.targetValue! * 0.8); // 80% of target considered complete
    }
    return false;
  }

  /// Calculate completion percentage for current month
  /// TASK 3: Fixed to use strict date boundaries
  double calculateMonthCompletionPercentage() {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 0);
    
    return _calculateCompletionPercentageForPeriod(startOfMonth, endOfMonth);
  }

  /// Calculate completion percentage for current year
  /// TASK 3: Fixed to use strict date boundaries
  double calculateYearCompletionPercentage() {
    final now = DateTime.now();
    final startOfYear = DateTime(now.year, 1, 1);
    final endOfYear = DateTime(now.year, 12, 31);
    
    return _calculateCompletionPercentageForPeriod(startOfYear, endOfYear);
  }

  /// Calculate completion percentage for a specific period
  /// TASK 3: Fixed to use strict date boundaries and prevent future date inclusion
  double _calculateCompletionPercentageForPeriod(DateTime start, DateTime end) {
    int totalDays = 0;
    int completedDays = 0;
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    
    DateTime current = start;
    while (current.isBefore(end.add(const Duration(days: 1)))) {
      final currentDate = DateTime(current.year, current.month, current.day);
      
      // TASK 3: Only count days up to today (don't include future dates)
      if (currentDate.isAfter(today)) {
        break;
      }
      
      totalDays++;
      
      final entryForDate = entries.where((entry) {
        final entryDate = DateTime(entry.timestamp.year, entry.timestamp.month, entry.timestamp.day);
        return entryDate == currentDate;
      }).firstOrNull;

      if (entryForDate != null && _isEntryCompleted(entryForDate)) {
        completedDays++;
      }
      
      current = current.add(const Duration(days: 1));
    }

    return totalDays > 0 ? (completedDays / totalDays) * 100 : 0.0;
  }

  /// Calculate total completions
  int calculateTotalCompletions() {
    return entries.where(_isEntryCompleted).length;
  }

  /// Get frequency breakdown by day of week
  Map<String, int> calculateFrequencyByDayOfWeek() {
    final frequency = <String, int>{
      'Mon': 0,
      'Tue': 0,
      'Wed': 0,
      'Thu': 0,
      'Fri': 0,
      'Sat': 0,
      'Sun': 0,
    };

    final dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

    for (final entry in entries) {
      if (_isEntryCompleted(entry)) {
        final dayOfWeek = entry.timestamp.weekday - 1; // Monday = 0
        frequency[dayNames[dayOfWeek]] = frequency[dayNames[dayOfWeek]]! + 1;
      }
    }

    return frequency;
  }

  /// TASK 1: Calculate frequency by day of week returning Map<int, int>
  /// where key is weekday (1 for Monday, 7 for Sunday) and value is completion count
  Map<int, int> calculateFrequencyByDay() {
    final frequency = <int, int>{
      1: 0, // Monday
      2: 0, // Tuesday
      3: 0, // Wednesday
      4: 0, // Thursday
      5: 0, // Friday
      6: 0, // Saturday
      7: 0, // Sunday
    };

    for (final entry in entries) {
      if (_isEntryCompleted(entry)) {
        final weekday = entry.timestamp.weekday; // 1-7 (Mon-Sun)
        final adjustedWeekday = weekday == 7 ? 7 : weekday; // Keep Sunday as 7
        frequency[adjustedWeekday] = frequency[adjustedWeekday]! + 1;
      }
    }

    return frequency;
  }

  /// Get score data for chart
  Future<List<ChartDataPoint>> getScoreDataForChart(TimeScale timeScale) async {
    final now = DateTime.now();
    final dataPoints = <ChartDataPoint>[];

    // Enhanced debugging
    developer.log('=== GENERATING SCORE CHART DATA ===');
    developer.log('TimeScale: ${timeScale.name}');
    developer.log('Total entries available: ${entries.length}');

    // Get the earliest entry date to start from
    final earliestEntryDate = entries.isEmpty 
        ? now.subtract(const Duration(days: 84)) // Default to 12 weeks ago
        : entries.map((e) => e.timestamp).reduce((a, b) => a.isBefore(b) ? a : b);
    
    final today = DateTime(now.year, now.month, now.day);
    
    developer.log('Earliest entry date: $earliestEntryDate');
    developer.log('Today: $today');

    switch (timeScale) {
      case TimeScale.day:
        // Generate daily data points
        final startDate = DateTime(earliestEntryDate.year, earliestEntryDate.month, earliestEntryDate.day);
        DateTime currentDate = startDate;
        
        while (currentDate.isBefore(today.add(const Duration(days: 1)))) {
          final nextDate = currentDate.add(const Duration(days: 1));
          final score = _calculateScoreForPeriod(currentDate, nextDate.subtract(const Duration(days: 1)));
          
          dataPoints.add(ChartDataPoint(
            date: currentDate,
            value: score,
            label: '${currentDate.month}/${currentDate.day}',
            isFuture: currentDate.isAfter(today),
            xValue: dataPoints.length.toDouble(),
          ));
          
          currentDate = nextDate;
        }
        break;

      case TimeScale.week:
        // Generate weekly data points using proper week boundaries
        DateTime currentDate = DateTime(earliestEntryDate.year, earliestEntryDate.month, earliestEntryDate.day);
        final processedWeeks = <String>{};
        int index = 0; // Sequential index for X-axis coordinates
        
        while (currentDate.isBefore(today.add(const Duration(days: 7)))) {
          // Get week boundaries for current date
          final weekBoundaries = await getWeekBoundaries(currentDate);
          final weekKey = '${weekBoundaries['startDate']!.year}-${_getWeekNumber(weekBoundaries['startDate']!)}';
          
          // Check if we've already processed this week
          if (!processedWeeks.contains(weekKey)) {
            processedWeeks.add(weekKey);
            
            // Calculate completion percentage for this week (0-100 scale)
            final completedDays = _calculateCompletedDaysInPeriod(
              weekBoundaries['startDate']!, 
              weekBoundaries['endDate']!
            );
            final totalDays = 7;
            final percentage = (completedDays / totalDays) * 100;
            
            final weekNumber = _getWeekNumber(weekBoundaries['startDate']!);
            final label = 'W$weekNumber';
            
            // Mandatory debugging log
            developer.log('Generated Score Point - Index: $index, Label: "$label", Percentage: $percentage% ($completedDays/$totalDays days)');
            
            // Add data point with sequential index and percentage value
            dataPoints.add(ChartDataPoint(
              date: weekBoundaries['startDate']!,
              value: percentage,
              label: label,
              isFuture: weekBoundaries['startDate']!.isAfter(today),
              xValue: index.toDouble(),
            ));
            
            index++; // Increment index for next data point
          }
          
          // Advance by 7 days to next week
          currentDate = currentDate.add(const Duration(days: 7));
        }
        break;

      case TimeScale.month:
        // Generate monthly data points
        DateTime currentDate = DateTime(earliestEntryDate.year, earliestEntryDate.month, 1);
        
        while (currentDate.isBefore(DateTime(today.year, today.month + 1, 1))) {
          final nextMonth = DateTime(currentDate.year, currentDate.month + 1, 1);
          final endOfMonth = nextMonth.subtract(const Duration(days: 1));
          
          final score = _calculateScoreForPeriod(currentDate, endOfMonth);
          
          dataPoints.add(ChartDataPoint(
            date: currentDate,
            value: score,
            label: '${currentDate.year}-${currentDate.month.toString().padLeft(2, '0')}',
            isFuture: currentDate.isAfter(today),
            xValue: dataPoints.length.toDouble(),
          ));
          
          currentDate = nextMonth;
        }
        break;

      default:
        // For other time scales, use simplified logic
        final startDate = _getStartDateForTimeScale(now, timeScale);
        final endDate = now.add(const Duration(days: 28));

        DateTime current = startDate;
        while (current.isBefore(endDate.add(const Duration(days: 1)))) {
          final score = _calculateScoreUpToDate(current);
          final bool isFutureDate = current.isAfter(now);
          dataPoints.add(ChartDataPoint(
            date: current,
            value: score,
            label: _formatDateForTimeScale(current, timeScale),
            isFuture: isFutureDate,
            xValue: dataPoints.length.toDouble(),
          ));

          current = _getNextDateForTimeScale(current, timeScale);
        }
        break;
    }

    return dataPoints;
  }

  /// Get history data for bar chart
  Future<List<ChartDataPoint>> getHistoryDataForChart(TimeScale timeScale) async {
    final now = DateTime.now();
    final dataPoints = <ChartDataPoint>[];

    switch (timeScale) {
      case TimeScale.week:
        // Get the earliest entry date to start from
        final earliestEntryDate = entries.isEmpty 
            ? now.subtract(const Duration(days: 84)) // Default to 12 weeks ago
            : entries.map((e) => e.timestamp).reduce((a, b) => a.isBefore(b) ? a : b);
        
        final startDate = DateTime(earliestEntryDate.year, earliestEntryDate.month, earliestEntryDate.day);
        final today = DateTime(now.year, now.month, now.day);
        
        DateTime currentDate = startDate;
        final processedWeeks = <String>{};
        int index = 0; // Sequential index for X-axis coordinates
        
        while (currentDate.isBefore(today.add(const Duration(days: 7)))) {
          // Get week boundaries for current date
          final weekBoundaries = await getWeekBoundaries(currentDate);
          final weekKey = '${weekBoundaries['startDate']!.year}-${_getWeekNumber(weekBoundaries['startDate']!)}';
          
          // Check if we've already processed this week
          if (!processedWeeks.contains(weekKey)) {
            processedWeeks.add(weekKey);
            
            // Calculate count for this week period
            final count = _calculateCountForPeriod(
              habit, 
              weekBoundaries['startDate']!, 
              weekBoundaries['endDate']!, 
              entries
            );
            
            final weekNumber = _getWeekNumber(weekBoundaries['startDate']!);
            final label = 'W$weekNumber';
            
            // Add debugging log
            developer.log('History Week $weekNumber: Start: ${weekBoundaries['startDate']}, End: ${weekBoundaries['endDate']}, Count: $count, Index: $index');
            
            // Add data point with sequential index
            dataPoints.add(ChartDataPoint(
              date: weekBoundaries['startDate']!,
              value: count.toDouble(),
              label: label,
              isFuture: weekBoundaries['startDate']!.isAfter(today),
              xValue: index.toDouble(),
            ));
            
            index++; // Increment index for next data point
          }
          
          // Advance by 7 days to next week
          currentDate = currentDate.add(const Duration(days: 7));
        }
        break;
        
      default:
        // For other time scales, use the original logic
        final startDate = _getStartDateForTimeScale(now, timeScale);
        
        // Determine future extension based on time scale
        final futureDuration = timeScale == TimeScale.day 
            ? const Duration(days: 7)  // 7 days for day view
            : const Duration(days: 28); // 4 weeks (28 days) for other views
        
        final endDate = now.add(futureDuration);
        
        developer.log('DEBUG History Chart | Generating data from $startDate to $endDate');

        DateTime current = startDate;
        while (current.isBefore(endDate.add(const Duration(days: 1)))) {
          final completions = _getCompletionsForPeriod(current, timeScale);
          final bool isFutureDate = current.isAfter(now);
          dataPoints.add(ChartDataPoint(
            date: current,
            value: completions.toDouble(),
            label: _formatDateForTimeScale(current, timeScale),
            isFuture: isFutureDate,
            xValue: dataPoints.length.toDouble(),
          ));

          current = _getNextDateForTimeScale(current, timeScale);
        }
        break;
    }

    return dataPoints;
  }

  /// Get completions for calendar
  Map<DateTime, bool> getCompletionsForCalendar() {
    final completions = <DateTime, bool>{};

    for (final entry in entries) {
      final date = DateTime(entry.timestamp.year, entry.timestamp.month, entry.timestamp.day);
      completions[date] = _isEntryCompleted(entry);
    }

    return completions;
  }

  /// Task 3: Get heatmap data as a Set<DateTime> of all completion dates
  /// This method provides the completion dates for the ActivityHeatmap widget
  Set<DateTime> getHeatmapData() {
    final completionDates = <DateTime>{};

    for (final entry in entries) {
      if (_isEntryCompleted(entry)) {
        final date = DateTime(entry.timestamp.year, entry.timestamp.month, entry.timestamp.day);
        completionDates.add(date);
      }
    }

    debugPrint('[HABIT_ANALYTICS] Generated heatmap data with ${completionDates.length} completion dates');
    return completionDates;
  }

  /// Helper methods for date calculations
  DateTime _getStartDateForTimeScale(DateTime date, TimeScale timeScale) {
    switch (timeScale) {
      case TimeScale.day:
        return date.subtract(const Duration(days: 30)); // Last 30 days
      case TimeScale.week:
        return date.subtract(const Duration(days: 84)); // Last 12 weeks
      case TimeScale.month:
        return DateTime(date.year - 1, date.month, date.day); // Last 12 months
      case TimeScale.quarter:
        return DateTime(date.year - 2, date.month, date.day); // Last 8 quarters
      case TimeScale.year:
        return DateTime(date.year - 5, date.month, date.day); // Last 5 years
    }
  }

  DateTime _getNextDateForTimeScale(DateTime date, TimeScale timeScale) {
    switch (timeScale) {
      case TimeScale.day:
        return date.add(const Duration(days: 1));
      case TimeScale.week:
        return date.add(const Duration(days: 7));
      case TimeScale.month:
        return DateTime(date.year, date.month + 1, date.day);
      case TimeScale.quarter:
        return DateTime(date.year, date.month + 3, date.day);
      case TimeScale.year:
        return DateTime(date.year + 1, date.month, date.day);
    }
  }

  String _formatDateForTimeScale(DateTime date, TimeScale timeScale) {
    switch (timeScale) {
      case TimeScale.day:
        return '${date.month}/${date.day}';
      case TimeScale.week:
        return 'W${_getWeekOfYear(date)}';
      case TimeScale.month:
        return '${date.year}-${date.month.toString().padLeft(2, '0')}';
      case TimeScale.quarter:
        return 'Q${((date.month - 1) ~/ 3) + 1} ${date.year}';
      case TimeScale.year:
        return date.year.toString();
    }
  }

  int _getWeekOfYear(DateTime date) {
    final firstDayOfYear = DateTime(date.year, 1, 1);
    final daysSinceFirstDay = date.difference(firstDayOfYear).inDays;
    return (daysSinceFirstDay / 7).ceil();
  }

  /// Get week number for a given date (used for chart data)
  int _getWeekNumber(DateTime date) {
    final firstDayOfYear = DateTime(date.year, 1, 1);
    final daysSinceFirstDay = date.difference(firstDayOfYear).inDays;
    return (daysSinceFirstDay / 7).floor() + 1;
  }

  /// Calculate score for a specific period using habit strength formula
  double _calculateScoreForPeriod(DateTime startDate, DateTime endDate) {
    // Enhanced debugging log
    developer.log('Calculating score for period: $startDate to $endDate');
    
    // Filter entries within the period
    final relevantEntries = entries.where((entry) {
      final entryDate = DateTime(entry.timestamp.year, entry.timestamp.month, entry.timestamp.day);
      return (entryDate.isAfter(startDate) || entryDate.isAtSameMomentAs(startDate)) && 
             (entryDate.isBefore(endDate) || entryDate.isAtSameMomentAs(endDate));
    }).toList();

    developer.log('Found ${relevantEntries.length} entries for period $startDate to $endDate');

    if (relevantEntries.isEmpty) {
      developer.log('No entries found, returning score 0.0');
      return 0.0;
    }

    // Sort entries by date
    relevantEntries.sort((a, b) => a.timestamp.compareTo(b.timestamp));

    double score = 0.0;
    final multiplier = _getMultiplierForHabitFrequency();

    for (final entry in relevantEntries) {
      final checkmarkValue = _getCheckmarkValue(entry);
      score = score * multiplier + checkmarkValue * (1 - multiplier);
      developer.log('Entry ${entry.id}: checkmark=$checkmarkValue, running_score=$score');
    }

    final finalScore = score.clamp(0.0, 1.0);
    developer.log('Final score for period: $finalScore');
    return finalScore;
  }

  /// Calculate count of completed entries for a specific period
  int _calculateCountForPeriod(Habit habit, DateTime startDate, DateTime endDate, List<Entry> allEntries) {
    // Filter entries within the period
    final relevantEntries = allEntries.where((entry) {
      final entryDate = DateTime(entry.timestamp.year, entry.timestamp.month, entry.timestamp.day);
      return (entryDate.isAfter(startDate) || entryDate.isAtSameMomentAs(startDate)) && 
             (entryDate.isBefore(endDate) || entryDate.isAtSameMomentAs(endDate));
    }).toList();

    // Count completed entries
    return relevantEntries.where(_isEntryCompleted).length;
  }

  /// Calculate number of completed days in a specific period
  int _calculateCompletedDaysInPeriod(DateTime startDate, DateTime endDate) {
    int completedDays = 0;
    DateTime current = startDate;
    
    while (current.isBefore(endDate.add(const Duration(days: 1)))) {
      final currentDate = DateTime(current.year, current.month, current.day);
      
      // Check if habit was completed on this date
      final entryForDate = entries.where((entry) {
        final entryDate = DateTime(entry.timestamp.year, entry.timestamp.month, entry.timestamp.day);
        return entryDate == currentDate;
      }).firstOrNull;

      if (entryForDate != null && _isEntryCompleted(entryForDate)) {
        completedDays++;
      }
      
      current = current.add(const Duration(days: 1));
    }
    
    return completedDays;
  }

  double _calculateScoreUpToDate(DateTime date) {
    final relevantEntries = entries.where((entry) {
      return entry.timestamp.isBefore(date.add(const Duration(days: 1)));
    }).toList();

    if (relevantEntries.isEmpty) return 0.0;

    relevantEntries.sort((a, b) => a.timestamp.compareTo(b.timestamp));

    double score = 0.0;
    final multiplier = _getMultiplierForHabitFrequency();

    for (final entry in relevantEntries) {
      final checkmarkValue = _getCheckmarkValue(entry);
      score = score * multiplier + checkmarkValue * (1 - multiplier);
    }

    return score.clamp(0.0, 1.0);
  }

  int _getCompletionsForPeriod(DateTime startDate, TimeScale timeScale) {
    final endDate = _getNextDateForTimeScale(startDate, timeScale);
    
    return entries.where((entry) {
      return entry.timestamp.isAfter(startDate.subtract(const Duration(days: 1))) &&
             entry.timestamp.isBefore(endDate) &&
             _isEntryCompleted(entry);
    }).length;
  }

  /// PHASE 2: Get proper calendar week boundaries based on user preference
  /// Returns a map with 'startDate' and 'endDate' keys
  Future<Map<String, DateTime>> getWeekBoundaries(DateTime date) async {
    final settingsService = SettingsService.instance;
    final startOfWeekDay = await settingsService.getStartOfWeek();
    
    debugPrint('[HABIT_ANALYTICS] Getting week boundaries for $date with start day: ${settingsService.getStartOfWeekDisplayName(startOfWeekDay)}');
    
    // Convert date to date-only (no time component)
    final dateOnly = DateTime(date.year, date.month, date.day);
    
    // Calculate the start of the week
    DateTime startDate;
    if (startOfWeekDay == SettingsService.SUNDAY) {
      // Week starts on Sunday
      final currentWeekday = dateOnly.weekday; // 1=Monday, 7=Sunday
      final daysSinceSunday = currentWeekday == 7 ? 0 : currentWeekday; // Sunday=0, Monday=1, etc.
      startDate = dateOnly.subtract(Duration(days: daysSinceSunday));
    } else {
      // Week starts on Monday
      final currentWeekday = dateOnly.weekday; // 1=Monday, 7=Sunday
      final daysSinceMonday = currentWeekday - 1; // Monday=0, Tuesday=1, etc.
      startDate = dateOnly.subtract(Duration(days: daysSinceMonday));
    }
    
    // End date is 6 days after start date
    final endDate = startDate.add(const Duration(days: 6));
    
    debugPrint('[HABIT_ANALYTICS] Week boundaries calculated: $startDate to $endDate');
    
    return {
      'startDate': startDate,
      'endDate': endDate,
    };
  }

  /// Calculate completion percentage for any time scale with proper date boundaries
  /// Returns (completed_days / total_days_so_far) * 100
  Future<double> calculateCompletionPercentage(TimeScale timeScale) async {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    
    DateTime startDate;
    DateTime endDate;
    
    switch (timeScale) {
      case TimeScale.week:
        // Use the existing getWeekBoundaries() helper method for correct start of week
        final weekBoundaries = await getWeekBoundaries(now);
        startDate = weekBoundaries['startDate']!;
        endDate = weekBoundaries['endDate']!;
        break;
      case TimeScale.month:
        // Period from first day of current month to today
        startDate = DateTime(now.year, now.month, 1);
        endDate = DateTime(now.year, now.month + 1, 0); // Last day of current month
        break;
      case TimeScale.year:
        // Period from January 1st of current year to today
        startDate = DateTime(now.year, 1, 1);
        endDate = DateTime(now.year, 12, 31); // Last day of current year
        break;
      default:
        throw ArgumentError('Unsupported time scale: $timeScale');
    }
    
    // Count total days and completed days
    int totalDays = 0;
    int completedDays = 0;
    
    DateTime current = startDate;
    while (current.isBefore(endDate.add(const Duration(days: 1)))) {
      final currentDate = DateTime(current.year, current.month, current.day);
      
      // Only count days up to today (don't include future dates)
      if (currentDate.isAfter(today)) {
        break;
      }
      
      totalDays++;
      
      // Check if habit was completed on this date
      if (habit.isCompletedOnDate(currentDate)) {
        completedDays++;
      }
      
      current = current.add(const Duration(days: 1));
    }
    
    // Calculate percentage with division by zero protection
    final percentage = totalDays > 0 ? (completedDays / totalDays) * 100 : 0.0;
    
    // Mandatory debugging log
    developer.log('DEBUG Percentage | Scale: ${timeScale.name}, Start: $startDate, End: $endDate, Completed: $completedDays, Total: $totalDays, Result: ${percentage.toStringAsFixed(1)}%');
    
    return percentage;
  }

  /// PHASE 2: Calculate "This Week %" using proper calendar week
  Future<double> calculateThisWeekPercentage() async {
    return calculateCompletionPercentage(TimeScale.week);
  }

  /// ROBUST TIMELINE DATA: Generate complete timeline for smooth scrolling
  Future<List<ChartDataPoint>> getTimelineScoreData(TimeScale timeScale) async {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final dataPoints = <ChartDataPoint>[];
    
    developer.log('=== GENERATING ROBUST TIMELINE DATA ===');
    developer.log('TimeScale: ${timeScale.name}');
    developer.log('Total entries: ${entries.length}');
    
    // Determine timeline range based on data availability
    final earliestEntryDate = entries.isEmpty 
        ? now.subtract(const Duration(days: 30)) // Default range
        : entries.map((e) => e.timestamp).reduce((a, b) => a.isBefore(b) ? a : b);
    
    switch (timeScale) {
      case TimeScale.day:
        await _generateDailyTimelineData(dataPoints, earliestEntryDate, today);
        break;
        
      case TimeScale.week:
        await _generateWeeklyTimelineData(dataPoints, earliestEntryDate, today);
        break;
        
      case TimeScale.month:
        await _generateMonthlyTimelineData(dataPoints, earliestEntryDate, today);
        break;
        
      default:
        throw ArgumentError('Unsupported time scale: $timeScale');
    }
    
    developer.log('Generated ${dataPoints.length} robust timeline data points');
    return dataPoints;
  }

  /// Generate daily timeline data
  Future<void> _generateDailyTimelineData(List<ChartDataPoint> dataPoints, DateTime earliestDate, DateTime today) async {
    final startDate = DateTime(earliestDate.year, earliestDate.month, earliestDate.day);
    final endDate = today.add(const Duration(days: 2)); // 2-day future buffer
    
    DateTime currentDate = startDate;
    while (currentDate.isBefore(endDate.add(const Duration(days: 1)))) {
      final score = _calculateDayScore(currentDate);
      final isFuture = currentDate.isAfter(today);
      
      // For structured timeline: use -1.0 for no data, 0.0 for incomplete, 100.0 for complete
      dataPoints.add(ChartDataPoint(
        date: currentDate,
        value: score, // Already returns -1.0, 0.0, or 100.0
        label: '${currentDate.day}',
        isFuture: isFuture,
        xValue: dataPoints.length.toDouble(),
      ));
      
      currentDate = currentDate.add(const Duration(days: 1));
    }
  }

  /// Generate weekly timeline data
  Future<void> _generateWeeklyTimelineData(List<ChartDataPoint> dataPoints, DateTime earliestDate, DateTime today) async {
    DateTime currentDate = DateTime(earliestDate.year, earliestDate.month, earliestDate.day);
    final endDate = today.add(const Duration(days: 14)); // 2-week future buffer
    final processedWeeks = <String>{};
    
    while (currentDate.isBefore(endDate)) {
      final weekBoundaries = await getWeekBoundaries(currentDate);
      final weekKey = '${weekBoundaries['startDate']!.year}-${_getWeekNumber(weekBoundaries['startDate']!)}';
      
      if (!processedWeeks.contains(weekKey)) {
        processedWeeks.add(weekKey);
        
        // Check if there are any entries in this week
        final hasEntries = _hasAnyEntriesInPeriod(weekBoundaries['startDate']!, weekBoundaries['endDate']!);
        
        double value;
        if (!hasEntries) {
          value = -1.0; // No data indicator
        } else {
          final completedDays = _calculateCompletedDaysInPeriod(
            weekBoundaries['startDate']!, 
            weekBoundaries['endDate']!
          );
          value = (completedDays / 7) * 100; // 0-100 percentage
        }
        
        final isFuture = weekBoundaries['startDate']!.isAfter(today);
        
        dataPoints.add(ChartDataPoint(
          date: weekBoundaries['startDate']!,
          value: value,
          label: 'W${_getWeekNumber(weekBoundaries['startDate']!)}',
          isFuture: isFuture,
          xValue: dataPoints.length.toDouble(),
        ));
      }
      
      currentDate = currentDate.add(const Duration(days: 7));
    }
  }

  /// Generate monthly timeline data
  Future<void> _generateMonthlyTimelineData(List<ChartDataPoint> dataPoints, DateTime earliestDate, DateTime today) async {
    final startDate = DateTime(earliestDate.year, earliestDate.month, 1);
    final endDate = DateTime(today.year, today.month + 2, 1); // 2-month future buffer
    
    DateTime currentDate = startDate;
    while (currentDate.isBefore(endDate)) {
      final nextMonth = DateTime(currentDate.year, currentDate.month + 1, 1);
      final endOfMonth = nextMonth.subtract(const Duration(days: 1));
      
      // Check if there are any entries in this month
      final hasEntries = _hasAnyEntriesInPeriod(currentDate, endOfMonth);
      
      double value;
      if (!hasEntries) {
        value = -1.0; // No data indicator
      } else {
        final completedDays = _calculateCompletedDaysInPeriod(currentDate, endOfMonth);
        final totalDays = endOfMonth.day;
        value = totalDays > 0 ? (completedDays / totalDays) * 100 : 0.0;
      }
      
      final isFuture = currentDate.isAfter(today);
      
      dataPoints.add(ChartDataPoint(
        date: currentDate,
        value: value,
        label: _getMonthLabel(currentDate),
        isFuture: isFuture,
        xValue: dataPoints.length.toDouble(),
      ));
      
      currentDate = nextMonth;
    }
  }

  /// Calculate score for a single day (returns -1.0 for no data, 0.0 for incomplete, 100.0 for complete)
  double _calculateDayScore(DateTime date) {
    final entryForDate = entries.where((entry) {
      final entryDate = DateTime(entry.timestamp.year, entry.timestamp.month, entry.timestamp.day);
      return entryDate == date;
    }).firstOrNull;
    
    if (entryForDate == null) {
      return -1.0; // No data indicator
    }
    
    return _isEntryCompleted(entryForDate) ? 100.0 : 0.0;
  }

  /// Calculate score for a single week (returns percentage 0-100)
  Future<double> _calculateWeekScore(DateTime weekStartDate) async {
    final weekBoundaries = await getWeekBoundaries(weekStartDate);
    final completedDays = _calculateCompletedDaysInPeriod(
      weekBoundaries['startDate']!, 
      weekBoundaries['endDate']!
    );
    return (completedDays / 7) * 100;
  }

  /// Calculate score for a single month (returns percentage 0-100)
  double _calculateMonthScore(DateTime monthStartDate) {
    final nextMonth = DateTime(monthStartDate.year, monthStartDate.month + 1, 1);
    final endOfMonth = nextMonth.subtract(const Duration(days: 1));
    
    final completedDays = _calculateCompletedDaysInPeriod(monthStartDate, endOfMonth);
    final totalDays = endOfMonth.day;
    
    return totalDays > 0 ? (completedDays / totalDays) * 100 : 0.0;
  }
  
  /// Calculate score for a single day (0-100 percentage)
  double _calculateScoreForSingleDay(DateTime date) {
    final entryForDate = entries.where((entry) {
      final entryDate = DateTime(entry.timestamp.year, entry.timestamp.month, entry.timestamp.day);
      return entryDate == date;
    }).firstOrNull;
    
    if (entryForDate == null) {
      return -1.0; // No data indicator
    }
    
    return _isEntryCompleted(entryForDate) ? 100.0 : 0.0;
  }
  
  /// Get month label with smart year display
  String _getMonthLabel(DateTime date) {
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                       'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    
    if (date.month == 1) {
      // Show year for January
      return date.year.toString();
    } else {
      // Show month name
      return monthNames[date.month - 1];
    }
  }

  /// NEW PAGED DESIGN: Get exactly 14 data points for fixed-layout chart
  Future<List<ChartDataPoint>> getPagedScoreData(Habit habit, TimeScale timeScale, DateTime endDate) async {
    developer.log('=== PAGED SCORE DATA GENERATION ===');
    developer.log('Habit: ${habit.name}, TimeScale: ${timeScale.name}, EndDate: $endDate');
    
    final dataPoints = <ChartDataPoint>[];
    
    // Generate exactly 14 data points with strict calendar periods
    for (int i = 13; i >= 0; i--) {
      DateTime periodStart, periodEnd;
      String label;
      
      switch (timeScale) {
        case TimeScale.day:
          // Strict calendar day boundaries
          periodStart = DateTime(endDate.year, endDate.month, endDate.day - i);
          periodEnd = DateTime(periodStart.year, periodStart.month, periodStart.day, 23, 59, 59);
          label = '${periodStart.day}';
          break;
          
        case TimeScale.week:
          // Calculate strict calendar week boundaries (Sunday-Saturday or Monday-Sunday)
          final targetDate = endDate.subtract(Duration(days: i * 7));
          final weekBoundaries = await getWeekBoundaries(targetDate);
          periodStart = weekBoundaries['startDate']!;
          periodEnd = weekBoundaries['endDate']!.add(const Duration(hours: 23, minutes: 59, seconds: 59));
          final weekNumber = _getWeekNumber(periodStart);
          label = 'W$weekNumber';
          break;
          
        case TimeScale.month:
          // Strict calendar month boundaries
          final targetMonth = endDate.month - i;
          final targetYear = endDate.year + (targetMonth <= 0 ? ((targetMonth - 1) ~/ 12) : 0);
          final adjustedMonth = targetMonth <= 0 ? (targetMonth % 12) + 12 : targetMonth;
          
          periodStart = DateTime(targetYear, adjustedMonth, 1);
          periodEnd = DateTime(targetYear, adjustedMonth + 1, 0, 23, 59, 59); // Last day of month
          label = _getMonthLabel(periodStart);
          break;
          
        case TimeScale.quarter:
          // Calculate quarter boundaries for the period ending i quarters before endDate
          final targetQuarter = ((endDate.month - 1) ~/ 3) + 1; // Current quarter (1-4)
          final targetYear = endDate.year;
          final quartersBack = i;
          
          // Calculate the target quarter and year
          int finalQuarter = targetQuarter - quartersBack;
          int finalYear = targetYear;
          
          while (finalQuarter <= 0) {
            finalQuarter += 4;
            finalYear -= 1;
          }
          
          // Calculate start and end of the quarter
          final quarterStartMonth = (finalQuarter - 1) * 3 + 1;
          periodStart = DateTime(finalYear, quarterStartMonth, 1);
          periodEnd = DateTime(finalYear, quarterStartMonth + 3, 0, 23, 59, 59); // Last day of quarter
          label = 'Q$finalQuarter';
          
          developer.log('Quarter calculation: i=$i, targetQ=$targetQuarter, finalQ=$finalQuarter, year=$finalYear, label=$label');
          break;
          
        case TimeScale.year:
          // Strict calendar year boundaries
          final targetYear = endDate.year - i;
          periodStart = DateTime(targetYear, 1, 1);
          periodEnd = DateTime(targetYear, 12, 31, 23, 59, 59);
          label = targetYear.toString();
          
          developer.log('Year calculation: i=$i, targetYear=$targetYear, label=$label');
          break;
          
        default:
          throw ArgumentError('Unsupported time scale: $timeScale');
      }
      
      // Calculate completion percentage for this period using strict date boundaries
      final score = _calculatePagedCompletionPercentage(periodStart, periodEnd);
      
      // Create data point with sequential xValue
      final xValue = (13 - i).toDouble();
      dataPoints.add(ChartDataPoint(
        date: periodStart,
        value: score == 0.0 && !_hasAnyEntriesInPeriod(periodStart, periodEnd) ? -1.0 : score,
        label: label,
        isFuture: periodStart.isAfter(DateTime.now()),
        xValue: xValue,
      ));
      
      developer.log('Generated point $xValue: $label = ${score == -1.0 ? "NO_DATA" : "${score.toStringAsFixed(1)}%"} (${periodStart.toIso8601String().substring(0, 10)} to ${periodEnd.toIso8601String().substring(0, 10)})');
    }
    
    developer.log('Generated ${dataPoints.length} paged data points with strict calendar periods');
    return dataPoints;
  }
  
  /// Check if there are any entries in the given period
  bool _hasAnyEntriesInPeriod(DateTime startDate, DateTime endDate) {
    return entries.any((entry) {
      return entry.timestamp.isAfter(startDate.subtract(const Duration(milliseconds: 1))) &&
             entry.timestamp.isBefore(endDate.add(const Duration(milliseconds: 1)));
    });
  }
  
  /// Calculate completion percentage for paged chart data (returns 0-100)
  double _calculatePagedCompletionPercentage(DateTime startDate, DateTime endDate) {
    int totalDays = 0;
    int completedDays = 0;
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    
    DateTime current = DateTime(startDate.year, startDate.month, startDate.day);
    final end = DateTime(endDate.year, endDate.month, endDate.day);
    
    debugPrint('[HABIT_ANALYTICS] Calculating paged completion for period: $startDate to $endDate');
    
    while (current.isBefore(end.add(const Duration(days: 1)))) {
      // Only count days up to today (don't include future dates)
      if (current.isAfter(today)) {
        break;
      }
      
      totalDays++;
      
      // Check if habit was completed on this date
      final entryForDate = entries.where((entry) {
        final entryDate = DateTime(entry.timestamp.year, entry.timestamp.month, entry.timestamp.day);
        return entryDate == current;
      }).firstOrNull;

      if (entryForDate != null && _isEntryCompleted(entryForDate)) {
        completedDays++;
      }
      
      current = current.add(const Duration(days: 1));
    }

    final percentage = totalDays > 0 ? (completedDays / totalDays) * 100 : 0.0;
    debugPrint('[HABIT_ANALYTICS] Paged period completion: $completedDays/$totalDays days = ${percentage.toStringAsFixed(1)}%');
    return percentage;
  }
  
}

