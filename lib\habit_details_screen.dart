import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:two_dimensional_scrollables/two_dimensional_scrollables.dart';
import 'dart:developer' as developer;
import 'dart:math' as math;
import 'habit.dart';
import 'section.dart';
import 'entry.dart';
import 'database_service.dart';
import 'habit_analytics_service.dart';
import 'modern_widgets.dart';
import 'modern_theme.dart';
import 'section_color_palette.dart';
import 'enhanced_entry_dialog.dart';
import 'settings_service.dart';
import 'linked_scroll_controller_group.dart';
import 'timeline_chart_painter.dart';

class HabitDetailsScreen extends StatefulWidget {
  final Habit habit;

  const HabitDetailsScreen({super.key, required this.habit});

  @override
  State<HabitDetailsScreen> createState() => _HabitDetailsScreenState();
}

class _HabitDetailsScreenState extends State<HabitDetailsScreen> {
  final DatabaseService _databaseService = DatabaseService();
  late Habit _currentHabit;
  List<Entry> _entries = [];
  List<Section> _sections = [];
  late HabitAnalyticsService _analyticsService;
  TimeScale _scoreChartTimeScale = TimeScale.week;
  TimeScale _historyChartTimeScale = TimeScale.week;
  bool _isLoading = true;
  
  // Removed old PageController - using new smooth scrolling timeline

  // Task 2: Add ScrollControllers for charts
  late ScrollController _scoreChartController;
  late ScrollController _historyChartController;
  late ScrollController _heatmapController;

  @override
  void initState() {
    super.initState();
    _currentHabit = widget.habit;

    // Task 2: Initialize ScrollControllers
    _scoreChartController = ScrollController();
    _historyChartController = ScrollController();
    _heatmapController = ScrollController();
    
    // Removed old PageController initialization

    _loadData();
  }

  @override
  void dispose() {
    // Task 2: Dispose ScrollControllers
    _scoreChartController.dispose();
    _historyChartController.dispose();
    _heatmapController.dispose();
    // Removed old PageController disposal
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    try {
      final results = await Future.wait([
        _databaseService.loadEntriesForHabit(_currentHabit.id),
        _databaseService.loadAllSections(),
      ]);

      _entries = results[0] as List<Entry>;
      _sections = results[1] as List<Section>;
      _analyticsService = HabitAnalyticsService(
        habit: _currentHabit,
        entries: _entries,
      );

      // Task 2: Set initial scroll positions after data loads
      _setInitialScrollPositions();
    } catch (e) {
      debugPrint('Error loading habit details data: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// Task 1 & 2: Set initial scroll positions to show recent data
  void _setInitialScrollPositions() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Task 1: Initialize Score Chart and History Chart to show most recent data
      _scrollToRecentData(_scoreChartController, _scoreChartTimeScale);
      _scrollToRecentData(_historyChartController, _historyChartTimeScale);

      // Task 2: Initialize Activity Heatmap to show current month
      _scrollHeatmapToCurrentMonth();
    });
  }

  /// Task 1: Scroll charts to show recent data (most recent data on the right)
  void _scrollToRecentData(ScrollController controller, TimeScale timeScale) {
    if (!controller.hasClients) return;

    try {
      final maxScrollExtent = controller.position.maxScrollExtent;
      if (maxScrollExtent <= 0) return;

      // Task 1: Jump to maximum scroll extent to show most recent data
      controller.jumpTo(maxScrollExtent);
      debugPrint(
        '[HABIT_DETAILS_SCREEN] Scrolled ${controller.runtimeType} to show recent data at offset: $maxScrollExtent',
      );
    } catch (e) {
      debugPrint('[HABIT_DETAILS_SCREEN] Error setting scroll position: $e');
    }
  }

  /// Task 2: Scroll heatmap to show current month
  void _scrollHeatmapToCurrentMonth() {
    if (!_heatmapController.hasClients) return;

    try {
      // Calculate offset to show the final (current) week
      final weeks = _generateHeatmapData();
      final totalWeeks = weeks.length;
      final cellWidth = 32.0;
      final visibleWeeks = 7; // Approximate number of weeks visible at once
      
      // Calculate offset to show the last few weeks (current week)
      final targetOffset = (totalWeeks - visibleWeeks) * cellWidth;
      final maxScrollExtent = _heatmapController.position.maxScrollExtent;
      final finalOffset = targetOffset.clamp(0.0, maxScrollExtent);
      
      debugPrint('DEBUG HeatmapScroll | Jumping to initial offset: $finalOffset');
      _heatmapController.jumpTo(finalOffset);
      debugPrint('[HABIT_DETAILS_SCREEN] Scrolled heatmap to show current week.');
    } catch (e) {
      debugPrint('[HABIT_DETAILS_SCREEN] Error scrolling heatmap: $e');
    }
  }

  /// Generate heatmap data for the last 3 months
  List<List<DateTime?>> _generateHeatmapData() {
    debugPrint('DEBUG HeatmapData | Starting to generate heatmap data');
    
    final now = DateTime.now();
    final startDate = DateTime(now.year, now.month - 3, 1); // Last 3 months
    final endDate = now;
    
    debugPrint('DEBUG HeatmapData | Date range: $startDate to $endDate');
    
    final weeks = <List<DateTime?>>[];
    
    // Start from the beginning of the first week that contains startDate
    final firstWeekday = startDate.weekday % 7; // Convert to 0-6 (Sun-Sat)
    DateTime weekStart = startDate.subtract(Duration(days: firstWeekday));
    
    while (weekStart.isBefore(endDate) || weekStart.isAtSameMomentAs(endDate)) {
      final week = <DateTime?>[];
      
      // Generate exactly 7 days for this week (Sun-Sat)
      for (int i = 0; i < 7; i++) {
        final date = weekStart.add(Duration(days: i));
        
        // Include all dates, but mark out-of-range as null for empty cells
        if (date.isBefore(startDate) || date.isAfter(endDate)) {
          week.add(null); // Empty cell for dates outside range
        } else {
          week.add(date);
        }
      }
      
      weeks.add(week);
      weekStart = weekStart.add(Duration(days: 7));
      
      // Prevent infinite loop
      if (weeks.length > 50) {
        debugPrint('DEBUG HeatmapData | Breaking to prevent infinite loop');
        break;
      }
    }
    
    debugPrint('DEBUG HeatmapData | Generated ${weeks.length} weeks of data');
    return weeks;
  }

  Future<void> _refreshData() async {
    // Reload the habit to get updated data
    final updatedHabit = await _databaseService.getHabitById(_currentHabit.id);
    if (updatedHabit != null) {
      setState(() => _currentHabit = updatedHabit);
    }
    await _loadData();
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('[HABIT_DETAILS_SCREEN] === BUILDING HABIT DETAILS SCREEN ===');
    debugPrint('[HABIT_DETAILS_SCREEN] Habit: ${_currentHabit.name}');
    debugPrint('[HABIT_DETAILS_SCREEN] Loading state: $_isLoading');
    debugPrint('[HABIT_DETAILS_SCREEN] Entries count: ${_entries.length}');
    debugPrint('[HABIT_DETAILS_SCREEN] Sections count: ${_sections.length}');

    final theme = Theme.of(context);
    final isDarkTheme = theme.brightness == Brightness.dark;

    debugPrint(
      '[HABIT_DETAILS_SCREEN] Theme brightness: ${isDarkTheme ? 'Dark' : 'Light'}',
    );

    return Scaffold(
      appBar: _buildAppBar(theme),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: EdgeInsets.all(
                ModernTheme.spaceSM * 0.95,
              ), // Additional 5% reduction
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildHeaderSection(theme, isDarkTheme),
                  SizedBox(height: ModernTheme.spaceSM * 0.95), // 5% reduction
                  _buildScoreChart(theme),
                  SizedBox(height: ModernTheme.spaceSM * 0.95), // 5% reduction
                  _buildHistoryChart(theme),
                  SizedBox(height: ModernTheme.spaceSM * 0.95), // 5% reduction
                  _buildStreaksAndFrequency(theme),
                  SizedBox(height: ModernTheme.spaceSM * 0.95), // 5% reduction
                  _buildHeatmapCalendar(theme),
                ],
              ),
            ),
    );
  }

  PreferredSizeWidget _buildAppBar(ThemeData theme) {
    return AppBar(
      title: Text(
        _currentHabit.name,
        style: GoogleFonts.inter(
          fontWeight: FontWeight.w600,
          fontSize:
              (18 *
              0.95), // Additional 5% reduction - Fixed: removed .round() for double type
        ),
      ),
      actions: [
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert),
          onSelected: _handleMenuSelection,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit_outlined),
                  SizedBox(width: 8),
                  Text('Edit'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete_outlined, color: Colors.red),
                  SizedBox(width: 8),
                  Text('Delete', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _handleMenuSelection(String value) {
    switch (value) {
      case 'edit':
        _showEditHabitDialog();
        break;
      case 'delete':
        _showDeleteConfirmation();
        break;
    }
  }

  Future<void> _showEditHabitDialog() async {
    // This would open the same dialog used for creating habits, pre-populated
    // For now, we'll show a placeholder
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Habit'),
        content: const Text('Edit functionality will be implemented here.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _refreshData();
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  Future<void> _showDeleteConfirmation() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Habit'),
        content: Text(
          'Are you sure you want to delete "${_currentHabit.name}"? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await _databaseService.deleteHabit(_currentHabit.id);
      if (mounted) {
        Navigator.pop(context);
      }
    }
  }

  Widget _buildHeaderSection(ThemeData theme, bool isDarkTheme) {
    debugPrint('[HABIT_DETAILS_SCREEN] === BUILDING HEADER SECTION ===');

    final section = _getHabitSection();
    final sectionColor = section != null
        ? SectionColorPalette.getColorFromHex(
            section.color,
            isDarkTheme: isDarkTheme,
          )
        : theme.colorScheme.primary;

    debugPrint('[HABIT_DETAILS_SCREEN] Section: ${section?.name ?? 'None'}');
    debugPrint(
      '[HABIT_DETAILS_SCREEN] Section color: ${section?.color ?? 'Default'}',
    );

    final daysSinceCreated = _calculateDaysSinceCreated();
    final createdDate = _getCreatedDate();
    final overallScore = _analyticsService.calculateHabitStrengthScore(TimeScale.year);

    // Use FutureBuilder for async percentage calculations
    return FutureBuilder<Map<String, double>>(
      future: _calculateAllPercentages(),
      builder: (context, snapshot) {
        final weekPercentage = snapshot.data?['week'] ?? 0.0;
        final monthPercentage = snapshot.data?['month'] ?? 0.0;
        final yearPercentage = snapshot.data?['year'] ?? 0.0;

        debugPrint('[HABIT_DETAILS_SCREEN] Calculated metrics:');
        debugPrint(
          '[HABIT_DETAILS_SCREEN] - Days since created: $daysSinceCreated',
        );
        debugPrint('[HABIT_DETAILS_SCREEN] - Created date: $createdDate');
        debugPrint(
          '[HABIT_DETAILS_SCREEN] - Overall score: ${(overallScore * 100).toInt()}%',
        );
        debugPrint(
          '[HABIT_DETAILS_SCREEN] - Week percentage: ${weekPercentage.toInt()}%',
        );
        debugPrint(
          '[HABIT_DETAILS_SCREEN] - Month percentage: ${monthPercentage.toInt()}%',
        );
        debugPrint(
          '[HABIT_DETAILS_SCREEN] - Year percentage: ${yearPercentage.toInt()}%',
        );

    return Row(
      children: [
        // Left Widget - Current Week Number
        Expanded(
          child: FutureBuilder<String>(
            future: HabitAnalyticsService.getCurrentWeekNumber(SettingsService.instance),
            builder: (context, snapshot) {
              final weekNumber = snapshot.data ?? 'W--';
              return Container(
                padding: EdgeInsets.all(
                  ModernTheme.spaceSM * 0.95,
                ), // Additional 5% reduction
                decoration: BoxDecoration(
                  color: theme.colorScheme.surface,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: theme.colorScheme.outline.withOpacity(0.2),
                    width: 1,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Current Week',
                      style: GoogleFonts.inter(
                        fontSize:
                            (12 *
                            0.95), // Additional 5% reduction - Fixed: removed .round()
                        fontWeight: FontWeight.w500,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                    SizedBox(height: ModernTheme.spaceXS * 0.95), // 5% reduction
                    Text(
                      weekNumber,
                      style: GoogleFonts.inter(
                        fontSize:
                            (32 *
                            0.95), // Additional 5% reduction - Fixed: removed .round()
                        fontWeight: FontWeight.bold,
                        color: sectionColor,
                      ),
                    ),
                    SizedBox(height: ModernTheme.spaceXS * 0.95), // 5% reduction
                    Text(
                      'Week of the year',
                      style: GoogleFonts.inter(
                        fontSize:
                            (11 *
                            0.95), // Additional 5% reduction - Fixed: removed .round()
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),

        SizedBox(width: ModernTheme.spaceSM * 0.95), // 5% reduction
        // Right Widget - Key Metrics
        Expanded(
          child: Container(
            padding: EdgeInsets.all(
              ModernTheme.spaceSM * 0.95,
            ), // Additional 5% reduction
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: theme.colorScheme.outline.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildMetricRow(
                  theme,
                  'Overall Score',
                  '${(overallScore * 100).toInt()}%',
                ),
                SizedBox(height: ModernTheme.spaceXS * 0.95), // 5% reduction
                _buildMetricRow(
                  theme,
                  'This Week %',
                  '${weekPercentage.toInt()}%',
                ),
                SizedBox(height: ModernTheme.spaceXS * 0.95), // 5% reduction
                _buildMetricRow(
                  theme,
                  'This Month %',
                  '${monthPercentage.toInt()}%',
                ),
                SizedBox(height: ModernTheme.spaceXS * 0.95), // 5% reduction
                _buildMetricRow(
                  theme,
                  'This Year %',
                  '${yearPercentage.toInt()}%',
                ),
              ],
            ),
          ),
        ),
      ],
    );
      },
    );
  }

  /// Calculate all percentages asynchronously
  Future<Map<String, double>> _calculateAllPercentages() async {
    final results = await Future.wait([
      _analyticsService.calculateCompletionPercentage(TimeScale.week),
      _analyticsService.calculateCompletionPercentage(TimeScale.month),
      _analyticsService.calculateCompletionPercentage(TimeScale.year),
    ]);
    
    return {
      'week': results[0],
      'month': results[1],
      'year': results[2],
    };
  }

  Widget _buildMetricRow(ThemeData theme, String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: GoogleFonts.inter(
            fontSize:
                (11 *
                0.95), // Additional 5% reduction - Fixed: removed .round()
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
        Text(
          value,
          style: GoogleFonts.inter(
            fontSize:
                (12 *
                0.95), // Additional 5% reduction - Fixed: removed .round()
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ],
    );
  }

  // Removed old PageController calculation methods

  Widget _buildScoreChart(ThemeData theme) {
    debugPrint('[HABIT_DETAILS_SCREEN] === BUILDING NEW SMOOTH SCROLLING SCORE CHART ===');
    debugPrint('[HABIT_DETAILS_SCREEN] Current time scale: $_scoreChartTimeScale');

    return ModernCard(
      padding: EdgeInsets.all(ModernTheme.spaceSM * 0.95),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Score Chart',
                style: GoogleFonts.inter(
                  fontSize: (16 * 0.95),
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              _buildTimeScaleButton(theme, _scoreChartTimeScale, (scale) {
                debugPrint('[HABIT_DETAILS_SCREEN] Score chart time scale changed to: $scale');
                setState(() {
                  _scoreChartTimeScale = scale;
                });
              }),
            ],
          ),
          SizedBox(height: ModernTheme.spaceSM * 0.95),
          // NEW SMOOTH SCROLLING TIMELINE
          SizedBox(
            height: (200 * 0.95).toDouble(),
            child: FutureBuilder<List<ChartDataPoint>>(
              future: _analyticsService.getTimelineScoreData(_scoreChartTimeScale),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }
                
                if (snapshot.hasError) {
                  return Center(child: Text('Error: ${snapshot.error}'));
                }
                
                if (!snapshot.hasData || snapshot.data!.isEmpty) {
                  return const Center(child: Text('No data available'));
                }
                
                final dataPoints = snapshot.data!;
                return _buildSmoothScrollableTimeline(theme, dataPoints);
              },
            ),
          ),
        ],
      ),
    );
  }

  /// WORKING TIMELINE CHART: Complete rebuild with proper functionality
  Widget _buildSmoothScrollableTimeline(ThemeData theme, List<ChartDataPoint> dataPoints) {
    debugPrint('[SCORE_CHART] Building working timeline with ${dataPoints.length} data points');
    
    if (dataPoints.isEmpty) {
      return Container(
        height: 190,
        child: const Center(
          child: Text(
            'No data available for this time period',
            style: TextStyle(fontSize: 14, color: Colors.grey),
          ),
        ),
      );
    }
    
    // Calculate chart dimensions
    const double pointSpacing = 70.0; // Optimal spacing for touch interaction
    const double chartHeight = 140.0;
    const double labelHeight = 50.0;
    final double totalContentWidth = dataPoints.length * pointSpacing + 100; // Extra padding
    final double screenWidth = MediaQuery.of(context).size.width - 32;
    final double containerWidth = math.max(totalContentWidth, screenWidth);
    
    return Container(
      height: chartHeight + labelHeight,
      decoration: BoxDecoration(
        border: Border.all(color: theme.colorScheme.outline.withOpacity(0.1)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: WorkingTimelineChart(
        dataPoints: dataPoints,
        theme: theme,
        timeScale: _scoreChartTimeScale,
        pointSpacing: pointSpacing,
        chartHeight: chartHeight,
        containerWidth: containerWidth,
      ),
    );
  }

  Widget _buildHistoryChart(ThemeData theme) {
    debugPrint('[HABIT_DETAILS_SCREEN] === BUILDING HISTORY CHART ===');
    debugPrint(
      '[HABIT_DETAILS_SCREEN] Current time scale: $_historyChartTimeScale',
    );

    return ModernCard(
      padding: EdgeInsets.all(
        ModernTheme.spaceSM * 0.95,
      ), // Additional 5% reduction
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'History Chart',
                style: GoogleFonts.inter(
                  fontSize:
                      (16 *
                      0.95), // Additional 5% reduction - Fixed: removed .round()
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              _buildTimeScaleButton(theme, _historyChartTimeScale, (scale) {
                debugPrint(
                  '[HABIT_DETAILS_SCREEN] History chart time scale changed to: $scale',
                );
                setState(() => _historyChartTimeScale = scale);
                // Task 1: Re-trigger scroll to recent data when switching to Day view
                if (scale == TimeScale.day) {
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    _scrollToRecentData(_historyChartController, scale);
                  });
                }
              }),
            ],
          ),
          SizedBox(height: ModernTheme.spaceSM * 0.95), // 5% reduction
          // Task 2: Implement sticky Y-axis for History Chart
          SizedBox(
            height: (200 * 0.95).toDouble(), // 5% reduction
            child: Row(
              children: [
                // Y-axis container with sufficient width to prevent overflow
                SizedBox(
                  width:
                      65.0, // Increased width to prevent "right overflowed by 6.0 pixels" error
                  child: _buildStickyYAxisForHistory(theme),
                ),
                // Task 2: Scrollable chart area
                Expanded(
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    controller: _historyChartController,
                    child: SizedBox(
                      width: _calculateChartWidth(_historyChartTimeScale),
                      child: _buildHistoryBarChart(
                        theme,
                        hideYAxis: true,
                      ), // Hide Y-axis in main chart
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeScaleButton(
    ThemeData theme,
    TimeScale currentScale,
    Function(TimeScale) onChanged,
  ) {
    return OutlinedButton(
      onPressed: () => _showTimeScaleMenu(theme, currentScale, onChanged),
      style: OutlinedButton.styleFrom(
        padding: EdgeInsets.symmetric(
          horizontal: ModernTheme.spaceSM * 0.95,
          vertical: ModernTheme.spaceXS * 0.95,
        ), // 5% reduction
        minimumSize: Size.zero,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            _getTimeScaleDisplayName(currentScale),
            style: GoogleFonts.inter(
              fontSize:
                  (12 *
                  0.95), // Additional 5% reduction - Fixed: removed .round()
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(width: 4 * 0.95), // 5% reduction
          Icon(
            Icons.keyboard_arrow_down,
            size: (16 * 0.95).toDouble(),
          ), // 5% reduction
        ],
      ),
    );
  }

  void _showTimeScaleMenu(
    ThemeData theme,
    TimeScale currentScale,
    Function(TimeScale) onChanged,
  ) {
    showMenu(
      context: context,
      position: const RelativeRect.fromLTRB(100, 100, 0, 0),
      items: TimeScale.values
          .map(
            (scale) => PopupMenuItem(
              value: scale,
              child: Text(_getTimeScaleDisplayName(scale)),
            ),
          )
          .toList(),
    ).then((value) {
      if (value != null) {
        onChanged(value);
      }
    });
  }

  String _getTimeScaleDisplayName(TimeScale scale) {
    switch (scale) {
      case TimeScale.day:
        return 'Day';
      case TimeScale.week:
        return 'Week';
      case TimeScale.month:
        return 'Month';
      case TimeScale.quarter:
        return 'Quarter';
      case TimeScale.year:
        return 'Year';
    }
  }

  Widget _buildStreaksAndFrequency(ThemeData theme) {
    debugPrint('[HABIT_DETAILS_SCREEN] === BUILDING STREAKS & FREQUENCY ===');

    final currentStreakData = _analyticsService
        .calculateCurrentStreakWithDates();
    final bestStreakData = _analyticsService.calculateBestStreakWithDates();
    final totalCompletions = _analyticsService.calculateTotalCompletions();

    debugPrint('[HABIT_DETAILS_SCREEN] Streak data:');
    debugPrint(
      '[HABIT_DETAILS_SCREEN] - Current streak: ${currentStreakData['length']}',
    );
    debugPrint(
      '[HABIT_DETAILS_SCREEN] - Current streak dates: ${currentStreakData['startDate']} to ${currentStreakData['endDate']}',
    );
    debugPrint(
      '[HABIT_DETAILS_SCREEN] - Best streak: ${bestStreakData['length']}',
    );
    debugPrint(
      '[HABIT_DETAILS_SCREEN] - Best streak dates: ${bestStreakData['startDate']} to ${bestStreakData['endDate']}',
    );
    debugPrint('[HABIT_DETAILS_SCREEN] - Total completions: $totalCompletions');

    return ModernCard(
      padding: EdgeInsets.all(
        ModernTheme.spaceSM * 0.95,
      ), // Additional 5% reduction
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Streaks & Frequency',
            style: GoogleFonts.inter(
              fontSize:
                  (16 *
                  0.95), // Additional 5% reduction - Fixed: removed .round()
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface,
            ),
          ),
          SizedBox(height: ModernTheme.spaceSM * 0.95), // 5% reduction
          Row(
            children: [
              Expanded(
                child: _buildStreakCardWithDates(
                  theme,
                  'Current Streak',
                  currentStreakData['length'] as int,
                  Colors.blue,
                  currentStreakData['startDate'] as DateTime?,
                  currentStreakData['endDate'] as DateTime?,
                ),
              ),
              SizedBox(width: ModernTheme.spaceSM * 0.95), // 5% reduction
              Expanded(
                child: _buildStreakCardWithDates(
                  theme,
                  'Best Streak',
                  bestStreakData['length'] as int,
                  Colors.green,
                  bestStreakData['startDate'] as DateTime?,
                  bestStreakData['endDate'] as DateTime?,
                ),
              ),
              SizedBox(width: ModernTheme.spaceSM * 0.95), // 5% reduction
              Expanded(
                child: _buildStreakCard(
                  theme,
                  'Total',
                  totalCompletions,
                  Colors.orange,
                ),
              ),
            ],
          ),
          SizedBox(height: ModernTheme.spaceSM * 0.95), // 5% reduction
          // TASK 1: Add Frequency by Day component
          _buildFrequencyByDay(theme),
        ],
      ),
    );
  }

  // TASK 1: Build Frequency by Day component
  Widget _buildFrequencyByDay(ThemeData theme) {
    debugPrint('[HABIT_DETAILS_SCREEN] === BUILDING FREQUENCY BY DAY ===');

    final frequencyByDay = _analyticsService.calculateFrequencyByDay();
    debugPrint('[HABIT_DETAILS_SCREEN] Frequency by day data: $frequencyByDay');

    // NEW: Use two-letter abbreviations for compact layout
    const dayNames = ['Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa', 'Su'];

    return Container(
      padding: EdgeInsets.all(
        ModernTheme.spaceSM * 0.95,
      ), // Additional 5% reduction
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.outline.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Frequency by Day',
            style: GoogleFonts.inter(
              fontSize: (14 * 0.95), // Additional 5% reduction
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface,
            ),
          ),
          SizedBox(height: ModernTheme.spaceSM * 0.95), // 5% reduction
          // NEW: Change from Wrap to Row with Expanded widgets for full width
          Row(
            children: List.generate(7, (index) {
              final weekday = index + 1; // 1-7 (Mon-Sun)
              final dayName = dayNames[index];
              final count = frequencyByDay[weekday] ?? 0;

              // Mandatory debugging for day labels
              debugPrint('DEBUG Frequency | Day Label: $dayName');

              return Expanded( // NEW: Wrap each chip in Expanded
                child: Container(
                  margin: EdgeInsets.symmetric(horizontal: 2.0), // Small margin between chips
                  padding: EdgeInsets.symmetric(
                    horizontal: 4.0, // Reduced horizontal padding for compact layout
                    vertical: ModernTheme.spaceXS * 0.95,
                  ), // 5% reduction
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primaryContainer.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: theme.colorScheme.primary.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    '$dayName: $count', // NEW: Use two-letter abbreviation
                    textAlign: TextAlign.center, // Center the text
                    style: GoogleFonts.inter(
                      fontSize: 11, // Adjust font size to ensure it never clips
                      fontWeight: FontWeight.w500,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                ),
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildStreakCard(
    ThemeData theme,
    String label,
    int value,
    Color color,
  ) {
    return Container(
      padding: EdgeInsets.all(
        ModernTheme.spaceSM * 0.95,
      ), // Additional 5% reduction
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(
            '$value',
            style: GoogleFonts.inter(
              fontSize:
                  (20 *
                  0.95), // Additional 5% reduction - Fixed: removed .round()
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          SizedBox(height: ModernTheme.spaceXS * 0.95), // 5% reduction
          Text(
            label,
            style: GoogleFonts.inter(
              fontSize:
                  (11 *
                  0.95), // Additional 5% reduction - Fixed: removed .round()
              color: theme.colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildStreakCardWithDates(
    ThemeData theme,
    String label,
    int value,
    Color color,
    DateTime? startDate,
    DateTime? endDate,
  ) {
    return Container(
      padding: EdgeInsets.all(
        ModernTheme.spaceSM * 0.95,
      ), // Additional 5% reduction
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(
            '$value',
            style: GoogleFonts.inter(
              fontSize:
                  (20 *
                  0.95), // Additional 5% reduction - Fixed: removed .round()
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          SizedBox(height: ModernTheme.spaceXS * 0.95), // 5% reduction
          Text(
            label,
            style: GoogleFonts.inter(
              fontSize:
                  (11 *
                  0.95), // Additional 5% reduction - Fixed: removed .round()
              color: theme.colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          if (startDate != null && endDate != null) ...[
            SizedBox(height: (ModernTheme.spaceXS * 0.95) / 2), // 5% reduction
            Text(
              '${_formatDateShort(startDate)} - ${_formatDateShort(endDate)}',
              style: GoogleFonts.inter(
                fontSize:
                    (9 *
                    0.95), // Additional 5% reduction - Fixed: removed .round()
                color: theme.colorScheme.onSurfaceVariant.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildHeatmapCalendar(ThemeData theme) {
    debugPrint('[HABIT_DETAILS_SCREEN] === BUILDING HEATMAP CALENDAR ===');
    debugPrint('[HABIT_DETAILS_SCREEN] Entries for heatmap: ${_entries.length}');

    // Generate heatmap data for the last 3 months
    final weeks = _generateHeatmapData();
    final heatmapData = _analyticsService.getHeatmapData();
    debugPrint('[HABIT_DETAILS_SCREEN] Heatmap completion dates: ${heatmapData.length}');

    return ModernCard(
      padding: EdgeInsets.all(ModernTheme.spaceSM * 0.95),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Activity Heatmap',
            style: GoogleFonts.inter(
              fontSize: (16 * 0.95),
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface,
            ),
          ),
          SizedBox(height: ModernTheme.spaceSM * 0.95),
          SizedBox(
            height: 280, // Height for 8 rows (1 month header + 7 weekdays) * 32px + padding
            child: Row(
              children: [
                // 1. The Scrollable Table - Wrapped in Expanded to fix overflow
                Expanded(
                  child: TableView.builder(
                    horizontalDetails: ScrollableDetails.horizontal(
                      controller: _heatmapController,
                    ),
                    columnCount: weeks.length,
                    rowCount: 8, // 1 for month headers, 7 for weekdays
                    columnBuilder: (index) => const TableSpan(
                      extent: FixedTableSpanExtent(28.0), // Reduced from 32.0 to 28.0
                    ),
                    rowBuilder: (index) => const TableSpan(
                      extent: FixedTableSpanExtent(28.0), // Reduced from 32.0 to 28.0
                    ),
                    cellBuilder: (context, vicinity) {
                      final row = vicinity.row;
                      final col = vicinity.column;
                      
                      if (row == 0) {
                        // Month Headers Row
                        return _buildMonthHeaderCell(theme, weeks, col);
                      } else {
                        // Date Cells (rows 1-7 for weekdays)
                        final weekdayIndex = row - 1; // Convert to 0-6
                        return _buildDateCell(theme, weeks, col, weekdayIndex, heatmapData);
                      }
                    },
                  ),
                ),
                // 2. The Sticky Weekday Labels on the Right - Reduced width
                SizedBox(
                  width: 32.0, // Reduced from 40.0 to 32.0
                  child: _buildVerticalWeekdayLabels(theme),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildScoreLineChart(ThemeData theme, {bool hideYAxis = false}) {
    debugPrint('[HABIT_DETAILS_SCREEN] === BUILDING SCORE LINE CHART ===');
    debugPrint('[HABIT_DETAILS_SCREEN] Time scale: $_scoreChartTimeScale');

    return FutureBuilder<List<ChartDataPoint>>(
      future: _analyticsService.getScoreDataForChart(_scoreChartTimeScale),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const Center(child: CircularProgressIndicator());
        }
        
        final dataPoints = snapshot.data!;
    debugPrint(
      '[HABIT_DETAILS_SCREEN] Score chart data points: ${dataPoints.length}',
    );

    if (dataPoints.isEmpty) {
      debugPrint('[HABIT_DETAILS_SCREEN] No data available for score chart');
      return Center(
        child: Text(
          'No data available',
          style: GoogleFonts.inter(color: theme.colorScheme.onSurfaceVariant),
        ),
      );
    }

    // Calculate Dynamic maxY for the Score Chart
    double maxScore = 0.0;
    if (dataPoints.isNotEmpty) {
      maxScore = dataPoints.map((p) => p.value).reduce((a, b) => a > b ? a : b);
    }
    
    // Add buffer with logic for scores up to 100%
    final double finalMaxY = (maxScore < 0.85) ? (maxScore + 0.15) : 1.05;
    
    // Mandatory debugging
    developer.log('DEBUG Score Chart MaxY | Initial Max: $maxScore, Final Max with Buffer: $finalMaxY');

    debugPrint('[HABIT_DETAILS_SCREEN] Score chart data points:');
    for (int i = 0; i < dataPoints.length; i++) {
      debugPrint(
        '[HABIT_DETAILS_SCREEN] [$i] ${dataPoints[i].label}: ${dataPoints[i].value}',
      );
    }

    return LineChart(
      LineChartData(
        gridData: FlGridData(
          show: true,
          drawVerticalLine: false,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: theme.colorScheme.outline.withOpacity(0.1),
              strokeWidth: 1,
            );
          },
        ),
        // Apply the dynamic maxY to prevent line touching top edge
        maxY: finalMaxY,
        minY: 0,
        titlesData: FlTitlesData(
          show: true,
          rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          bottomTitles: AxisTitles(
            axisNameWidget: Text(
              _getTimeScaleDisplayName(_scoreChartTimeScale),
              style: GoogleFonts.inter(
                fontSize: 10,
                color: theme.colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w500,
              ),
            ),
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              interval: 1, // TASK 1: Increase X-axis spacing - show every label
              getTitlesWidget: (value, meta) {
                final index = value.toInt();
                if (index >= 0 && index < dataPoints.length) {
                  // TASK 1: Format X-axis labels based on time scale
                  final formattedLabel = _formatChartLabel(
                    dataPoints[index].label,
                    _scoreChartTimeScale,
                    index,
                    dataPoints,
                  );
                  debugPrint(
                    '[HABIT_DETAILS_SCREEN] Score chart X-axis label [$index]: ${dataPoints[index].label} -> $formattedLabel',
                  );
                  return Padding(
                    padding: const EdgeInsets.only(top: 4),
                    child: Text(
                      formattedLabel,
                      style: GoogleFonts.inter(
                        fontSize: 9,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  );
                }
                return const Text('');
              },
            ),
          ),
          // Task 2: Hide Y-axis in main chart when sticky Y-axis is used
          leftTitles: AxisTitles(
            sideTitles: SideTitles(showTitles: !hideYAxis),
            axisNameWidget: hideYAxis
                ? null
                : Text(
                    'Score',
                    style: GoogleFonts.inter(
                      fontSize: 10,
                      color: theme.colorScheme.onSurfaceVariant,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
          ),
        ),
        borderData: FlBorderData(show: false),
        // In _buildScoreLineChart, before the 'return LineChart(...)'
        lineBarsData: [
          LineChartBarData(
            spots: _buildChartSpots(dataPoints, theme),
            isCurved: false, // TASK 1: Change curve to straight lines
            color: theme.colorScheme.primary,
            barWidth: 3,
            dotData: FlDotData(
              // TASK 1: Add visible data point dots
              show: true,
              getDotPainter: (spot, percent, barData, index) {
                final isFuture = dataPoints.length > index && dataPoints[index].isFuture;
                // In the getDotPainter callback, add a log to confirm that the isFuture flag is being read correctly.
                developer.log('DEBUG Dot Painter | Index: $index, isFuture: $isFuture');
                return FlDotCirclePainter(
                  radius: 4,
                  color: isFuture
                      ? theme.colorScheme.primary.withOpacity(0.5) // Faded color for future dots
                      : theme.colorScheme.primary,
                  strokeWidth: 2,
                  strokeColor: theme.colorScheme.surface,
                );
              },
            ),
          ),
        ],
      ),
    );
      },
    );
  }

  // Build chart spots with line break at current date
  List<FlSpot> _buildChartSpots(List<ChartDataPoint> dataPoints, ThemeData theme) {
    final List<FlSpot> chartSpots = [];
    bool breakInserted = false;
    for (int i = 0; i < dataPoints.length; i++) {
        final point = dataPoints[i];
        if (point.isFuture && !breakInserted) {
            chartSpots.add(FlSpot.nullSpot); // Insert the break
            breakInserted = true;
        }
        chartSpots.add(FlSpot(i.toDouble(), point.value.clamp(0.0, 1.0)));
    }
    
    // Inside _buildScoreLineChart in habit_details_screen.dart, add a developer.log() statement to print the final chartSpots list before it is passed to the LineChart.
    developer.log('DEBUG Score Chart Spots: $chartSpots');
    
    return chartSpots;
  }

  // Task 2: Build sticky Y-axis for Score Chart
  Widget _buildStickyYAxisForScore(ThemeData theme) {
    return FutureBuilder<List<ChartDataPoint>>(
      future: _analyticsService.getScoreDataForChart(_scoreChartTimeScale),
      builder: (context, snapshot) {
        if (!snapshot.hasData) return Container();
        
        final dataPoints = snapshot.data!;
    if (dataPoints.isEmpty) return Container();

    // Use same dynamic maxY calculation as main chart
    double maxScore = 0.0;
    if (dataPoints.isNotEmpty) {
      maxScore = dataPoints.map((p) => p.value).reduce((a, b) => a > b ? a : b);
    }
    final double finalMaxY = (maxScore < 0.85) ? (maxScore + 0.15) : 1.05;

    return LineChart(
      LineChartData(
        gridData: FlGridData(show: false),
        maxY: finalMaxY,
        minY: 0,
        titlesData: FlTitlesData(
          show: true,
          rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          bottomTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          leftTitles: AxisTitles(
            axisNameWidget: Text(
              'Score',
              style: GoogleFonts.inter(
                fontSize: 10,
                color: theme.colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w500,
              ),
            ),
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40,
              getTitlesWidget: (value, meta) {
                return Text(
                  '${(value * 100).toInt()}%',
                  style: GoogleFonts.inter(
                    fontSize: 9,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                );
              },
            ),
          ),
        ),
        borderData: FlBorderData(show: false),
        lineBarsData: [], // No data lines, just Y-axis
      ),
    );
      },
    );
  }

  Widget _buildHistoryBarChart(ThemeData theme, {bool hideYAxis = false}) {
    return FutureBuilder<List<ChartDataPoint>>(
      future: _analyticsService.getHistoryDataForChart(_historyChartTimeScale),
      builder: (context, snapshot) {
        // 1. Handle the loading state
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        // 2. Handle the error state
        if (snapshot.hasError) {
          return Center(child: Text('Error loading chart: ${snapshot.error}'));
        }

        // 3. Handle the empty/no data state
        if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return const Center(child: Text('No data available for this period.'));
        }

        // 4. THIS IS THE MOST IMPORTANT STEP:
        //    The data is now available. Declare a new final variable for it.
        final dataPoints = snapshot.data!;

        // 5. PASTE THE ORIGINAL CHART-BUILDING LOGIC HERE.
        debugPrint('[HABIT_DETAILS_SCREEN] === BUILDING HISTORY BAR CHART ===');
        debugPrint('[HABIT_DETAILS_SCREEN] Time scale: $_historyChartTimeScale');
        debugPrint(
          '[HABIT_DETAILS_SCREEN] History chart data points: ${dataPoints.length}',
        );

        // Calculate Dynamic maxY for the Chart
        double maxY = 5; // Default value
        if (dataPoints.isNotEmpty) {
          maxY = dataPoints.map((p) => p.value).reduce((a, b) => a > b ? a : b);
        }
        
        // Add buffer to create headroom for count labels
        final double finalMaxY = (maxY > 0) ? maxY * 1.25 : 5;
        
        // Mandatory debugging
        developer.log('DEBUG History Chart MaxY | Initial Max: $maxY, Final Max with Buffer: $finalMaxY');

        debugPrint('[HABIT_DETAILS_SCREEN] History chart data points:');
        for (int i = 0; i < dataPoints.length; i++) {
          debugPrint(
            '[HABIT_DETAILS_SCREEN] [$i] ${dataPoints[i].label}: ${dataPoints[i].value}',
          );
        }

        // Generate bar groups for chart
        final barGroups = dataPoints.asMap().entries.map((entry) {
          debugPrint(
            '[HABIT_DETAILS_SCREEN] History chart bar [${entry.key}]: ${entry.value.value}',
          );
          return BarChartGroupData(
            x: entry.key,
            barsSpace: 8, // TASK 1: Add spacing between bars
            barRods: [
              BarChartRodData(
                toY: entry.value.value,
                color: theme.colorScheme.primary,
                width: 20, // TASK 1: Slightly wider bars for better visibility
                borderRadius: BorderRadius.circular(4),
              ),
            ],
          );
        }).toList();

        // Mandatory debugging for default labels
        for (int i = 0; i < dataPoints.length; i++) {
          if (dataPoints[i].value > 0) {
            developer.log('DEBUG Creating Default Tooltip | Index: $i | Value: ${dataPoints[i].value}');
          }
        }

        return BarChart(
          BarChartData(
            gridData: FlGridData(show: false),
            // Apply the dynamic maxY to prevent clipped labels
            maxY: finalMaxY,
            minY: 0,
            titlesData: FlTitlesData(
              show: true,
              rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
              topTitles: AxisTitles(
                sideTitles: SideTitles(
                  showTitles: true,
                  reservedSize: 28, // Space for count labels above bars
                  getTitlesWidget: (double value, TitleMeta meta) {
                    final int index = value.toInt();
                    if (index >= 0 && index < dataPoints.length) {
                      final double barValue = dataPoints[index].value;
                      if (barValue > 0) {
                        return Text(
                          barValue.toInt().toString(),
                          style: GoogleFonts.inter(
                            fontSize: 10,
                            fontWeight: FontWeight.w600,
                            color: theme.colorScheme.onSurface,
                          ),
                        );
                      }
                    }
                    return Container(); // Return empty for zero-value bars
                  },
                ),
              ),
              bottomTitles: AxisTitles(
                axisNameWidget: Text(
                  _getTimeScaleDisplayName(_historyChartTimeScale),
                  style: GoogleFonts.inter(
                    fontSize: 10,
                    color: theme.colorScheme.onSurfaceVariant,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                sideTitles: SideTitles(
                  showTitles: true,
                  reservedSize: 35, // TASK 1: More space for labels
                  interval: 1, // TASK 1: Show every label to eliminate crowding
                  getTitlesWidget: (value, meta) {
                    final index = value.toInt();
                    if (index >= 0 && index < dataPoints.length) {
                      // TASK 1: Format X-axis labels based on time scale
                      final formattedLabel = _formatChartLabel(
                        dataPoints[index].label,
                        _historyChartTimeScale,
                        index,
                        dataPoints,
                      );
                      debugPrint(
                        '[HABIT_DETAILS_SCREEN] History chart X-axis label [$index]: ${dataPoints[index].label} -> $formattedLabel',
                      );
                      return Padding(
                        padding: const EdgeInsets.only(
                          top: 6,
                        ), // TASK 1: More padding for readability
                        child: Text(
                          formattedLabel,
                          style: GoogleFonts.inter(
                            fontSize: 9,
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      );
                    }
                    return const Text('');
                  },
                ),
              ),
              // Task 2: Hide Y-axis in main chart when sticky Y-axis is used
              leftTitles: AxisTitles(
                sideTitles: SideTitles(showTitles: !hideYAxis),
                axisNameWidget: hideYAxis
                    ? null
                    : Text(
                        'Count',
                        style: GoogleFonts.inter(
                          fontSize: 10,
                          color: theme.colorScheme.onSurfaceVariant,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
              ),
            ),
            borderData: FlBorderData(show: false),
            barGroups: barGroups,
          ),
        );
      },
    );
  }

  // Task 2: Build sticky Y-axis for History Chart
  Widget _buildStickyYAxisForHistory(ThemeData theme) {
    return FutureBuilder<List<ChartDataPoint>>(
      future: _analyticsService.getHistoryDataForChart(_historyChartTimeScale),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Container();
        }
        
        if (snapshot.hasError) {
          return Container();
        }
        
        if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return Container();
        }
        
        final dataPoints = snapshot.data!;

        // Use same dynamic maxY calculation as main chart
        double maxY = 5; // Default value
        if (dataPoints.isNotEmpty) {
          maxY = dataPoints.map((p) => p.value).reduce((a, b) => a > b ? a : b);
        }
        final double finalMaxY = (maxY > 0) ? maxY * 1.25 : 5;

        return BarChart(
          BarChartData(
            gridData: FlGridData(show: false),
            maxY: finalMaxY,
            minY: 0,
            titlesData: FlTitlesData(
              show: true,
              rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
              topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
              bottomTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
              leftTitles: AxisTitles(
                axisNameWidget: Text(
                  'Count',
                  style: GoogleFonts.inter(
                    fontSize: 10,
                    color: theme.colorScheme.onSurfaceVariant,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                sideTitles: SideTitles(
                  showTitles: true,
                  reservedSize: 40,
                  getTitlesWidget: (value, meta) {
                    return Text(
                      value.toInt().toString(),
                      style: GoogleFonts.inter(
                        fontSize: 9,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    );
                  },
                ),
              ),
            ),
            borderData: FlBorderData(show: false),
            barGroups: [], // No data bars, just Y-axis
          ),
        );
      },
    );
  }

  Section? _getHabitSection() {
    if (_currentHabit.sectionIds.isNotEmpty && _sections.isNotEmpty) {
      return _sections.firstWhere(
        (s) => s.id == _currentHabit.sectionIds.first,
        orElse: () => _sections.first,
      );
    }
    return null;
  }

  int _calculateDaysSinceCreated() {
    debugPrint('[HABIT_DETAILS_SCREEN] === CALCULATING DAYS SINCE CREATED ===');
    debugPrint('[HABIT_DETAILS_SCREEN] Total entries: ${_entries.length}');

    if (_entries.isEmpty) {
      debugPrint('[HABIT_DETAILS_SCREEN] No entries found, returning 0 days');
      return 0;
    }

    final earliestEntry = _entries.reduce(
      (a, b) => a.timestamp.isBefore(b.timestamp) ? a : b,
    );
    final daysSince = DateTime.now().difference(earliestEntry.timestamp).inDays;

    debugPrint(
      '[HABIT_DETAILS_SCREEN] Earliest entry: ${earliestEntry.timestamp}',
    );
    debugPrint('[HABIT_DETAILS_SCREEN] Days since created: $daysSince');

    return daysSince;
  }

  String _getCreatedDate() {
    if (_entries.isEmpty) return 'Unknown';
    final earliestEntry = _entries.reduce(
      (a, b) => a.timestamp.isBefore(b.timestamp) ? a : b,
    );
    return _formatDate(earliestEntry.timestamp);
  }

  String _formatDate(DateTime date) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }

  String _formatDateShort(DateTime date) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    return '${date.day} ${months[date.month - 1]}';
  }

  // TASK 1: Format chart labels based on time scale
  String _formatChartLabel(
    String originalLabel,
    TimeScale timeScale,
    int index,
    List dataPoints,
  ) {
    debugPrint('[HABIT_DETAILS_SCREEN] === FORMATTING CHART LABEL ===');
    debugPrint(
      '[HABIT_DETAILS_SCREEN] Original label: $originalLabel, TimeScale: $timeScale, Index: $index',
    );

    switch (timeScale) {
      case TimeScale.day:
        // TASK 1: Day view formatting - show only day number, or month name for first of month
        if (originalLabel.contains('/')) {
          final parts = originalLabel.split('/');
          if (parts.length >= 2) {
            final month = int.tryParse(parts[0]) ?? 1;
            final day = int.tryParse(parts[1]) ?? 1;

            // Special condition: If it's the first of a new month, show month name
            if (day == 1) {
              const months = [
                'Jan',
                'Feb',
                'Mar',
                'Apr',
                'May',
                'Jun',
                'Jul',
                'Aug',
                'Sep',
                'Oct',
                'Nov',
                'Dec',
              ];
              final formattedLabel = months[month - 1];
              debugPrint(
                '[HABIT_DETAILS_SCREEN] First of month detected: $formattedLabel',
              );
              return formattedLabel;
            } else {
              // Regular day - show only day number
              final formattedLabel = day.toString();
              debugPrint('[HABIT_DETAILS_SCREEN] Regular day: $formattedLabel');
              return formattedLabel;
            }
          }
        }
        break;

      case TimeScale.month:
        // TASK 1: Month view formatting - show month name, or year for January
        if (originalLabel.length >= 3) {
          // Check if this is a month label (e.g., "Nov", "Dec")
          const months = [
            'Jan',
            'Feb',
            'Mar',
            'Apr',
            'May',
            'Jun',
            'Jul',
            'Aug',
            'Sep',
            'Oct',
            'Nov',
            'Dec',
          ];

          // If it's already a month name, check if it's January (year change)
          if (months.contains(originalLabel)) {
            if (originalLabel == 'Jan' && index > 0) {
              // Special condition: January indicates year change
              final currentYear = DateTime.now().year;
              final formattedLabel = currentYear.toString();
              debugPrint(
                '[HABIT_DETAILS_SCREEN] Year change detected: $formattedLabel',
              );
              return formattedLabel;
            } else {
              debugPrint(
                '[HABIT_DETAILS_SCREEN] Regular month: $originalLabel',
              );
              return originalLabel;
            }
          }

          // If it's a date format, extract month
          if (originalLabel.contains('/')) {
            final parts = originalLabel.split('/');
            if (parts.length >= 2) {
              final month = int.tryParse(parts[0]) ?? 1;
              final monthName = months[month - 1];

              if (monthName == 'Jan' && index > 0) {
                final currentYear = DateTime.now().year;
                final formattedLabel = currentYear.toString();
                debugPrint(
                  '[HABIT_DETAILS_SCREEN] Year change from date: $formattedLabel',
                );
                return formattedLabel;
              } else {
                debugPrint(
                  '[HABIT_DETAILS_SCREEN] Month from date: $monthName',
                );
                return monthName;
              }
            }
          }
        }
        break;

      default:
        // For other time scales, return original label
        debugPrint(
          '[HABIT_DETAILS_SCREEN] Using original label for ${timeScale.name}: $originalLabel',
        );
        return originalLabel;
    }

    debugPrint(
      '[HABIT_DETAILS_SCREEN] Fallback to original label: $originalLabel',
    );
    return originalLabel;
  }

  // TASK 2: Calculate chart width based on time scale and viewport requirements
  double _calculateChartWidth(TimeScale timeScale) {
    debugPrint('[HABIT_DETAILS_SCREEN] === CALCULATING CHART WIDTH ===');
    debugPrint('[HABIT_DETAILS_SCREEN] TimeScale: $timeScale');

    switch (timeScale) {
      case TimeScale.day:
        // TASK 2: For day view, show exactly 13 days in viewport
        // Assuming screen width ~400px, 13 days should fit comfortably
        // Total width should accommodate more days for scrolling
        const dayWidth = 35.0; // Width per day
        const totalDays = 30; // Show 30 days total, 13 visible
        const calculatedWidth = dayWidth * totalDays;
        debugPrint(
          '[HABIT_DETAILS_SCREEN] Day view width: $calculatedWidth (13 days visible)',
        );
        return calculatedWidth;

      case TimeScale.week:
        const weekWidth = 50.0;
        const totalWeeks = 12;
        const calculatedWidth = weekWidth * totalWeeks;
        debugPrint('[HABIT_DETAILS_SCREEN] Week view width: $calculatedWidth');
        return calculatedWidth;

      case TimeScale.month:
        const monthWidth = 60.0;
        const totalMonths = 12;
        const calculatedWidth = monthWidth * totalMonths;
        debugPrint('[HABIT_DETAILS_SCREEN] Month view width: $calculatedWidth');
        return calculatedWidth;

      default:
        const defaultWidth = 600.0;
        debugPrint('[HABIT_DETAILS_SCREEN] Default width: $defaultWidth');
        return defaultWidth;
    }
  }

  // Calculate number of weeks for heatmap
  double _calculateNumberOfWeeks() {
    final now = DateTime.now();
    final startDate = DateTime(now.year, now.month - 5, 1); // Last 6 months
    final endDate = DateTime(now.year, now.month + 1, 0); // End of current month
    final totalDays = endDate.difference(startDate).inDays + 1;
    final numberOfWeeks = (totalDays / 7).ceil();
    debugPrint('[HABIT_DETAILS_SCREEN] Calculated number of weeks: $numberOfWeeks');
    return numberOfWeeks.toDouble();
  }

  // Build month header cell for TableView
  TableViewCell _buildMonthHeaderCell(ThemeData theme, List<List<DateTime?>> weeks, int col) {
    if (col >= weeks.length) {
      return TableViewCell(child: Container());
    }
    
    final week = weeks[col];
    final firstDate = week.firstWhere((date) => date != null, orElse: () => null);
    
    if (firstDate == null) {
      return TableViewCell(child: Container());
    }
    
    // Check if this is the start of a new month
    bool isNewMonth = false;
    if (col == 0) {
      isNewMonth = true;
    } else if (col > 0) {
      final prevWeek = weeks[col - 1];
      final prevDate = prevWeek.firstWhere((date) => date != null, orElse: () => null);
      if (prevDate != null && prevDate.month != firstDate.month) {
        isNewMonth = true;
      }
    }
    
    final Widget cellContent;
    if (isNewMonth) {
      const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      cellContent = Container(
        alignment: Alignment.center,
        child: Text(
          months[firstDate.month - 1],
          style: GoogleFonts.inter(
            fontSize: 10,
            fontWeight: FontWeight.w500,
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
      );
    } else {
      cellContent = Container();
    }
    
    return TableViewCell(child: cellContent);
  }

  // Build date cell for TableView
  TableViewCell _buildDateCell(ThemeData theme, List<List<DateTime?>> weeks, int col, int weekdayIndex, Set<DateTime> heatmapData) {
    if (col >= weeks.length || weekdayIndex >= 7) {
      return TableViewCell(child: Container());
    }
    
    final week = weeks[col];
    final date = week.length > weekdayIndex ? week[weekdayIndex] : null;
    
    if (date == null) {
      return TableViewCell(child: Container());
    }
    
    final isCompleted = heatmapData.contains(DateTime(date.year, date.month, date.day));
    
    final Widget cellContent = Container(
      margin: EdgeInsets.all(1),
      decoration: BoxDecoration(
        color: isCompleted 
            ? theme.colorScheme.primary 
            : theme.colorScheme.surface.withOpacity(0.3),
        borderRadius: BorderRadius.circular(3),
        border: Border.all(
          color: theme.colorScheme.outline.withOpacity(0.2),
          width: 0.5,
        ),
      ),
      child: Center(
        child: Text(
          '${date.day}',
          style: GoogleFonts.inter(
            fontSize: 8,
            fontWeight: FontWeight.w500,
            color: isCompleted 
                ? theme.colorScheme.onPrimary 
                : theme.colorScheme.onSurface,
          ),
        ),
      ),
    );
    
    return TableViewCell(child: cellContent);
  }

  // Build vertical weekday labels with proper alignment
  Widget _buildVerticalWeekdayLabels(ThemeData theme) {
    const weekdays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    
    return Column(
      children: [
        // Add placeholder for alignment with month header row
        const SizedBox(height: 28.0), // Match the new cell height
        
        // Generate weekday labels
        ...weekdays.map((day) => Container(
          width: 32, // Match the reduced label container width
          height: 28, // Match the new cell height
          child: Align(
            alignment: Alignment.centerRight,
            child: Text(
              day,
              style: GoogleFonts.inter(
                fontSize: 9,
                fontWeight: FontWeight.w500,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ),
        )).toList(),
      ],
    );
  }

  // Build weekday labels for sticky column (legacy method - kept for compatibility)
  List<Widget> _buildWeekdayLabels(ThemeData theme) {
    const weekdays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    
    return weekdays.map((day) => Container(
      width: 32, // Updated to match new width
      height: 28, // Updated to match new cell height
      child: Align(
        alignment: Alignment.centerRight,
        child: Text(
          day,
          style: GoogleFonts.inter(
            fontSize: 9,
            fontWeight: FontWeight.w500,
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
      ),
    )).toList();
  }

  // Build vertical weekday labels for heatmap (sticky labels on the right)
  Widget _buildVerticalWeekdayLabelsForHeatmap(ThemeData theme) {
    debugPrint('[HABIT_DETAILS_SCREEN] === BUILDING VERTICAL WEEKDAY LABELS FOR HEATMAP ===');
    debugPrint('[HABIT_DETAILS_SCREEN] Creating sticky weekday labels for heatmap with proper alignment');
    debugPrint('[HABIT_DETAILS_SCREEN] Adding offset to align weekday labels with date rows');
    
    const weekdays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        // Add this SizedBox to create the offset and push labels down
        const SizedBox(height: 24.0), // Pushes the labels down to align with date rows
        
        // The existing weekday labels
        ...weekdays.map((day) => Container(
          width: 40, // Wider to accommodate full day names
          height: 22, // Match heatmap cell height
          margin: EdgeInsets.only(bottom: 2),
          child: Align(
            alignment: Alignment.centerRight,
            child: Text(
              day, // Show full day name (not just first letter)
              style: GoogleFonts.inter(
                fontSize: (9 * 0.95),
                fontWeight: FontWeight.w500,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ),
        )).toList(),
      ],
    );
  }

}

// Custom Heatmap Calendar Widget - NEW: Row-by-row approach with synchronized scrolling
class HabitHeatmapCalendar extends StatefulWidget {
  final Habit habit;
  final List<Entry> entries;
  final Set<DateTime>? heatmapData;

  const HabitHeatmapCalendar({
    super.key,
    required this.habit,
    required this.entries,
    this.heatmapData,
  });

  @override
  State<HabitHeatmapCalendar> createState() => _HabitHeatmapCalendarState();
}

class _HabitHeatmapCalendarState extends State<HabitHeatmapCalendar> {
  @override
  void initState() {
    super.initState();
    debugPrint('DEBUG Heatmap: Initializing simplified heatmap calendar widget.');
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('[HABIT_DETAILS_SCREEN] === BUILDING SIMPLIFIED HEATMAP ===');
    
    try {
      final theme = Theme.of(context);
      final now = DateTime.now();
      final startDate = DateTime(now.year, now.month - 5, 1); // Last 6 months
      final endDate = now;
      
      debugPrint('DEBUG Heatmap Build: Start date: $startDate');
      debugPrint('DEBUG Heatmap Build: End date: $endDate');
      debugPrint('DEBUG Heatmap Build: Current date: $now');
      
      final weeks = _generateWeeks(startDate, endDate);
      debugPrint('DEBUG Heatmap Build: Generated ${weeks.length} weeks');
      
      final completions = _getCompletionsMap();
      debugPrint('DEBUG Heatmap Build: Found ${completions.length} completion dates');
      
      // Calculate total width based on number of weeks
      final totalWidth = weeks.length * 24.0;
      debugPrint('DEBUG HeatmapLayout | Total Calculated Width: $totalWidth');
      
      debugPrint('DEBUG Heatmap Build: Building simplified grid structure...');
      
      // Simplified structure - just the grid without internal scrolling or labels
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 1. Month Headers Row (without space for weekday labels)
          _buildSimplifiedMonthHeadersRow(theme, weeks),
          const SizedBox(height: 8),
          
          // 2. Main Grid - 7 rows for weekdays, no internal scrolling
          Column(
            children: List.generate(7, (weekdayIndex) {
              debugPrint('DEBUG Heatmap Build: Creating row for weekday index: $weekdayIndex');
              return _buildSimplifiedWeekdayRow(theme, weekdayIndex, weeks, completions);
            }),
          ),
        ],
      );
    } catch (e, stackTrace) {
      debugPrint('DEBUG Heatmap ERROR: Failed to build heatmap widget: $e');
      debugPrint('DEBUG Heatmap ERROR: Stack trace: $stackTrace');
      
      // Return error widget instead of crashing
      return Container(
        height: 200,
        padding: const EdgeInsets.all(16),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error, color: Theme.of(context).colorScheme.error),
              const SizedBox(height: 8),
              Text(
                'Heatmap Error: $e',
                style: TextStyle(color: Theme.of(context).colorScheme.error),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }
  }

  // Simplified method to build month headers without weekday label space
  Widget _buildSimplifiedMonthHeadersRow(ThemeData theme, List<List<DateTime?>> weeks) {
    debugPrint('[HABIT_DETAILS_SCREEN] === BUILDING SIMPLIFIED MONTH HEADERS ROW ===');
    
    final monthHeaders = <Widget>[];
    String? currentMonth;
    int weekCount = 0;
    
    for (int i = 0; i < weeks.length; i++) {
      final week = weeks[i];
      final firstDate = week.firstWhere(
        (date) => date != null,
        orElse: () => null,
      );
      
      if (firstDate != null) {
        final monthName = _getShortMonthName(firstDate);
        
        if (currentMonth != monthName) {
          if (currentMonth != null && weekCount > 0) {
            monthHeaders.add(
              SizedBox(
                width: weekCount * 24.0,
                child: Text(
                  currentMonth,
                  textAlign: TextAlign.center,
                  style: GoogleFonts.inter(
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ),
            );
          }
          currentMonth = monthName;
          weekCount = 1;
        } else {
          weekCount++;
        }
      }
    }
    
    // Add final month header
    if (currentMonth != null && weekCount > 0) {
      monthHeaders.add(
        SizedBox(
          width: weekCount * 24.0,
          child: Text(
            currentMonth,
            textAlign: TextAlign.center,
            style: GoogleFonts.inter(
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ),
      );
    }
    
    return Row(children: monthHeaders);
  }

  // Simplified method to build a weekday row without internal scrolling or labels
  Widget _buildSimplifiedWeekdayRow(
    ThemeData theme,
    int weekdayIndex,
    List<List<DateTime?>> weeks,
    Map<DateTime, bool> completions,
  ) {
    const weekdays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    final dayLabel = weekdays[weekdayIndex];
    debugPrint('DEBUG HeatmapRow | Building simplified row for $dayLabel');
    
    return Container(
      height: 22,
      margin: const EdgeInsets.only(bottom: 2),
      child: Row(
        children: weeks.map((week) {
          final date = week.length > weekdayIndex ? week[weekdayIndex] : null;
          return _buildFixedWidthHeatmapCell(theme, date, completions);
        }).toList(),
      ),
    );
  }

  // Helper method to get weekday label
  String _getWeekdayLabel(int weekdayIndex) {
    const weekdays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    
    if (weekdayIndex < 0 || weekdayIndex >= weekdays.length) {
      debugPrint('DEBUG HeatmapLabel ERROR: Invalid weekday index: $weekdayIndex');
      return 'ERR';
    }
    
    final label = weekdays[weekdayIndex];
    debugPrint('DEBUG HeatmapLabel: Weekday index $weekdayIndex -> $label');
    return label;
  }

  // Helper method to build day cells for a specific weekday
  List<Widget> _buildDayCellsForWeekday(
    ThemeData theme,
    int weekdayIndex,
    List<List<DateTime?>> weeks,
    Map<DateTime, bool> completions,
  ) {
    debugPrint('DEBUG HeatmapCells: Building day cells for weekday index $weekdayIndex');
    debugPrint('DEBUG HeatmapCells: Processing ${weeks.length} weeks');
    
    try {
      final cells = weeks.map((week) {
        final date = week.length > weekdayIndex ? week[weekdayIndex] : null;
        if (date != null) {
          debugPrint('DEBUG HeatmapCells: Creating cell for date: $date (completed: ${completions[date] ?? false})');
        } else {
          debugPrint('DEBUG HeatmapCells: Creating empty cell for weekday index $weekdayIndex');
        }
        return _buildFixedWidthHeatmapCell(theme, date, completions);
      }).toList();
      
      debugPrint('DEBUG HeatmapCells: Created ${cells.length} cells for weekday index $weekdayIndex');
      return cells;
    } catch (e, stackTrace) {
      debugPrint('DEBUG HeatmapCells ERROR: Failed to build day cells for weekday $weekdayIndex: $e');
      debugPrint('DEBUG HeatmapCells ERROR: Stack trace: $stackTrace');
      
      // Return empty list to prevent crash
      return [];
    }
  }

  // Generate weeks for the heatmap grid
  List<List<DateTime?>> _generateWeeks(DateTime startDate, DateTime endDate) {
    debugPrint('[HABIT_DETAILS_SCREEN] === GENERATING WEEKS ===');
    
    final weeks = <List<DateTime?>>[];
    
    // Start from the beginning of the first week that contains startDate
    final firstWeekday = startDate.weekday % 7; // Convert to 0-6 (Sun-Sat)
    DateTime weekStart = startDate.subtract(Duration(days: firstWeekday));
    
    while (weekStart.isBefore(endDate) || weekStart.isAtSameMomentAs(endDate)) {
      final week = <DateTime?>[];
      
      // Generate exactly 7 days for this week (Sun-Sat)
      for (int i = 0; i < 7; i++) {
        final date = weekStart.add(Duration(days: i));
        
        // Include all dates, but mark out-of-range as null for empty cells
        if (date.isBefore(startDate) || date.isAfter(endDate)) {
          week.add(null); // Empty cell for dates outside range
        } else {
          week.add(date);
        }
      }
      
      weeks.add(week);
      weekStart = weekStart.add(Duration(days: 7));
      
      // Prevent infinite loop
      if (weeks.length > 50) {
        debugPrint('[HABIT_DETAILS_SCREEN] Breaking to prevent infinite loop');
        break;
      }
    }
    
    debugPrint('[HABIT_DETAILS_SCREEN] Generated ${weeks.length} weeks');
    return weeks;
  }

  // Build month headers row with fixed widths
  Widget _buildMonthHeadersRow(ThemeData theme, List<List<DateTime?>> weeks) {
    debugPrint('[HABIT_DETAILS_SCREEN] === BUILDING MONTH HEADERS ROW ===');
    
    final monthHeaders = <Widget>[];
    String? currentMonth;
    int weekCount = 0;
    
    for (int i = 0; i < weeks.length; i++) {
      final week = weeks[i];
      final firstDate = week.firstWhere(
        (date) => date != null,
        orElse: () => null,
      );
      
      if (firstDate != null) {
        final monthName = _getShortMonthName(firstDate);
        
        if (currentMonth != monthName) {
          if (currentMonth != null && weekCount > 0) {
            monthHeaders.add(
              SizedBox(
                width: weekCount * 24.0,
                child: Text(
                  currentMonth,
                  textAlign: TextAlign.center,
                  style: GoogleFonts.inter(
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ),
            );
          }
          currentMonth = monthName;
          weekCount = 1;
        } else {
          weekCount++;
        }
      }
    }
    
    // Add final month header
    if (currentMonth != null && weekCount > 0) {
      monthHeaders.add(
        SizedBox(
          width: weekCount * 24.0,
          child: Text(
            currentMonth,
            textAlign: TextAlign.center,
            style: GoogleFonts.inter(
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ),
      );
    }
    
    return Row(
      children: [
        // Space for the scrollable content area
        Expanded(
          child: Row(children: monthHeaders),
        ),
        // Space for the weekday labels on the right
        SizedBox(width: 40.0),
      ],
    );
  }

  // Build weekday labels column
  Widget _buildWeekdayLabelsColumn(ThemeData theme) {
    const weekdays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    
    return SizedBox(
      width: 50,
      child: Column(
        children: weekdays.map((day) => Container(
          height: 22,
          margin: EdgeInsets.only(bottom: 2),
          child: Align(
            alignment: Alignment.centerRight,
            child: Text(
              day,
              style: GoogleFonts.inter(
                fontSize: 10,
                fontWeight: FontWeight.w500,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ),
        )).toList(),
      ),
    );
  }

  // Build vertical weekday labels
  Widget _buildVerticalWeekdayLabels(ThemeData theme) {
    const weekdays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    
    return SizedBox(
      width: 50,
      child: Column(
        children: weekdays.map((day) => Container(
          height: 22,
          margin: EdgeInsets.only(bottom: 2),
          child: Align(
            alignment: Alignment.centerRight,
            child: Text(
              day,
              style: GoogleFonts.inter(
                fontSize: 10,
                fontWeight: FontWeight.w500,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ),
        )).toList(),
      ),
    );
  }
  
  // Build weekday row without labels (for the main grid)
  Widget _buildWeekdayRowWithoutLabels(
    ThemeData theme,
    int weekdayIndex,
    List<List<DateTime?>> weeks,
    Map<DateTime, bool> completions,
  ) {
    const weekdays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    final dayLabel = weekdays[weekdayIndex];
    debugPrint('DEBUG Heatmap: Building row for $dayLabel');
    
    return Container(
      height: 22,
      margin: EdgeInsets.only(bottom: 2),
      child: Row(
        children: weeks.map((week) {
          final date = week.length > weekdayIndex ? week[weekdayIndex] : null;
          return _buildFixedWidthHeatmapCell(theme, date, completions);
        }).toList(),
      ),
    );
  }
  
  // Build fixed-width heatmap cell
  Widget _buildFixedWidthHeatmapCell(
    ThemeData theme,
    DateTime? date,
    Map<DateTime, bool> completions,
  ) {
    final isCompleted = date != null ? (completions[date] ?? false) : false;
    final isCurrentMonth = date != null
        ? (date.month >= DateTime.now().month - 5 && date.month <= DateTime.now().month)
        : false;

    return Container(
      width: 22,
      height: 22,
      margin: EdgeInsets.only(right: 2),
      decoration: BoxDecoration(
        color: date != null
            ? _getEnhancedHeatmapColor(theme, isCompleted, isCurrentMonth)
            : Colors.transparent,
        borderRadius: BorderRadius.circular(3),
        border: date != null && isCurrentMonth
            ? Border.all(
                color: theme.colorScheme.outline.withOpacity(0.2),
                width: 0.5,
              )
            : null,
      ),
      child: date != null
          ? Center(
              child: Text(
                '${date.day}',
                style: GoogleFonts.inter(
                  fontSize: 8,
                  fontWeight: FontWeight.w500,
                  color: isCompleted
                      ? theme.colorScheme.onPrimary
                      : theme.colorScheme.onSurface,
                ),
              ),
            )
          : null,
    );
  }

  // Build individual day cell (legacy method - kept for compatibility)
  Widget _buildDayCell(ThemeData theme, DateTime? date, Map<DateTime, bool> completions) {
    final isCompleted = date != null ? (completions[date] ?? false) : false;
    final isCurrentMonth = date != null
        ? (date.month >= DateTime.now().month - 5 && date.month <= DateTime.now().month)
        : false;

    return Container(
      width: 22,
      height: 22,
      margin: EdgeInsets.only(right: 2, bottom: 2),
      decoration: BoxDecoration(
        color: date != null
            ? _getEnhancedHeatmapColor(theme, isCompleted, isCurrentMonth)
            : Colors.transparent,
        borderRadius: BorderRadius.circular(3),
        border: date != null && isCurrentMonth
            ? Border.all(
                color: theme.colorScheme.outline.withOpacity(0.2),
                width: 0.5,
              )
            : null,
      ),
      child: date != null
          ? Center(
              child: Text(
                '${date.day}',
                style: GoogleFonts.inter(
                  fontSize: (8 * 0.95),
                  fontWeight: FontWeight.w500,
                  color: isCompleted
                      ? theme.colorScheme.onPrimary
                      : theme.colorScheme.onSurface,
                ),
              ),
            )
          : null,
    );
  }


  // TASK 2: Build proper week structure grid with correct date alignment
  Widget _buildProperWeekStructureGrid(
    ThemeData theme,
    DateTime startDate,
    DateTime endDate,
  ) {
    debugPrint(
      '[HABIT_DETAILS_SCREEN] === BUILDING PROPER WEEK STRUCTURE GRID ===',
    );

    final completions = _getCompletionsMap();
    debugPrint(
      '[HABIT_DETAILS_SCREEN] Completion dates found: ${completions.keys.length}',
    );

    // TASK 2: Generate weeks with proper date alignment
    final weeks = _generateProperWeeks(startDate, endDate);
    debugPrint('[HABIT_DETAILS_SCREEN] Generated ${weeks.length} weeks');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Month headers
        _buildMonthHeadersForWeeks(theme, weeks),
        SizedBox(height: ModernTheme.spaceXS * 0.95),

        // Grid only (no weekday labels - handled by new row-by-row approach)
        SizedBox(
          width: weeks.length * 24.0, // Fixed width based on number of weeks
          child: Column(
            children: List.generate(
              7,
              (weekdayIndex) => _buildWeekdayRowWithoutLabels(
                theme,
                weekdayIndex,
                weeks,
                completions,
              ),
            ),
          ),
        ),
      ],
    );
  }

  // TASK 3: Build horizontal month headers for day columns
  Widget _buildHorizontalMonthHeaders(
    ThemeData theme,
    List<Map<String, dynamic>> dayColumns,
  ) {
    debugPrint(
      '[HABIT_DETAILS_SCREEN] Building month headers for ${dayColumns.length} columns',
    );

    final monthHeaders = <Widget>[];
    String? currentMonth;
    int monthDayCount = 0;

    // Add spacing for weekday labels
    monthHeaders.add(SizedBox(width: 60)); // Space for weekday labels

    for (int i = 0; i < dayColumns.length; i++) {
      final date = dayColumns[i]['date'] as DateTime;
      final monthName = _getShortMonthName(date);

      if (currentMonth != monthName) {
        if (currentMonth != null && monthDayCount > 0) {
          // Add previous month header
          monthHeaders.add(
            SizedBox(
              width: monthDayCount * 24.0, // 24px per day column
              child: Text(
                currentMonth,
                textAlign: TextAlign.center,
                style: GoogleFonts.inter(
                  fontSize: (10 * 0.95),
                  fontWeight: FontWeight.w500,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ),
          );
        }
        currentMonth = monthName;
        monthDayCount = 1;
      } else {
        monthDayCount++;
      }
    }

    // Add final month header
    if (currentMonth != null && monthDayCount > 0) {
      monthHeaders.add(
        SizedBox(
          width: monthDayCount * 24.0,
          child: Text(
            currentMonth,
            textAlign: TextAlign.center,
            style: GoogleFonts.inter(
              fontSize: (10 * 0.95),
              fontWeight: FontWeight.w500,
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ),
      );
    }

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: monthHeaders,
    );
  }

  Widget _buildMonthGrid(
    ThemeData theme,
    Map<String, dynamic> monthData,
    Map<DateTime, bool> completions,
  ) {
    final weeks = monthData['weeks'] as List<List<DateTime>>;

    return Padding(
      padding: EdgeInsets.only(
        bottom: ModernTheme.spaceXS * 0.95 / 2,
      ), // 5% reduction
      child: Column(
        children: weeks
            .map((week) => _buildWeekRow(theme, week, completions))
            .toList(),
      ),
    );
  }

  Widget _buildWeekRow(
    ThemeData theme,
    List<DateTime> week,
    Map<DateTime, bool> completions,
  ) {
    return Padding(
      padding: EdgeInsets.only(
        bottom: (ModernTheme.spaceXS * 0.95) / 4,
      ), // 5% reduction
      child: Row(
        children: week
            .map((date) => _buildDayCell(theme, date, completions))
            .toList(),
      ),
    );
  }



  // TASK 3: Build a single day column (7 cells vertically)
  Widget _buildDayColumn(
    ThemeData theme,
    Map<String, dynamic> dayColumn,
    Map<DateTime, bool> completions,
  ) {
    final date = dayColumn['date'] as DateTime;
    final weekdayIndex = date.weekday % 7; // Convert to 0-6 (Sun-Sat)

    return Container(
      width: 22,
      margin: EdgeInsets.only(right: 2),
      child: Column(
        children: List.generate(7, (rowIndex) {
          // Calculate if this cell should show this date
          final shouldShowDate = rowIndex == weekdayIndex;

          return Container(
            height: 20,
            margin: EdgeInsets.only(bottom: 2),
            decoration: BoxDecoration(
              color: shouldShowDate
                  ? _getHeatmapColor(theme, completions[date] ?? false, true)
                  : Colors.transparent,
              borderRadius: BorderRadius.circular(3),
              border: shouldShowDate
                  ? Border.all(
                      color: theme.colorScheme.outline.withOpacity(0.1),
                      width: 0.5,
                    )
                  : null,
            ),
            child: shouldShowDate
                ? Center(
                    child: Text(
                      '${date.day}',
                      style: GoogleFonts.inter(
                        fontSize: (8 * 0.95),
                        fontWeight: FontWeight.w500,
                        // Task 3: AGGRESSIVE VERIFICATION - Red text to make change obvious
                        color: (completions[date] ?? false)
                            ? Colors
                                  .red // AGGRESSIVE VERIFICATION: Red text for immediate visibility
                            : theme.colorScheme.onSurface,
                      ),
                    ),
                  )
                : null,
          );
        }),
      ),
    );
  }

  // TASK 2: Generate proper weeks with correct date alignment (fixed algorithm)
  List<List<DateTime?>> _generateProperWeeks(
    DateTime startDate,
    DateTime endDate,
  ) {
    debugPrint(
      '[HABIT_DETAILS_SCREEN] === GENERATING PROPER WEEKS (TASK 2) ===',
    );
    debugPrint(
      '[HABIT_DETAILS_SCREEN] Generating proper weeks from $startDate to $endDate',
    );

    final weeks = <List<DateTime?>>[];

    // TASK 2: Start from the beginning of the first week that contains startDate
    final firstDate = DateTime(startDate.year, startDate.month, startDate.day);
    final firstWeekday = firstDate.weekday % 7; // Convert to 0-6 (Sun-Sat)
    DateTime weekStart = firstDate.subtract(Duration(days: firstWeekday));

    debugPrint(
      '[HABIT_DETAILS_SCREEN] First date: $firstDate, weekday: $firstWeekday',
    );
    debugPrint('[HABIT_DETAILS_SCREEN] Week start: $weekStart');

    while (weekStart.isBefore(endDate) || weekStart.isAtSameMomentAs(endDate)) {
      final week = <DateTime?>[];

      // TASK 2: Generate exactly 7 days for this week (Sun-Sat)
      for (int i = 0; i < 7; i++) {
        final date = weekStart.add(Duration(days: i));

        // TASK 2: Include all dates, but mark out-of-range as null for empty cells
        if (date.isBefore(startDate) || date.isAfter(endDate)) {
          week.add(null); // Empty cell for dates outside range
          debugPrint(
            '[HABIT_DETAILS_SCREEN] Empty cell for out-of-range date: $date',
          );
        } else {
          week.add(date);
          debugPrint('[HABIT_DETAILS_SCREEN] Added date: $date (weekday ${i})');
        }
      }

      weeks.add(week);
      weekStart = weekStart.add(Duration(days: 7));

      // Prevent infinite loop
      if (weeks.length > 50) {
        debugPrint('[HABIT_DETAILS_SCREEN] Breaking to prevent infinite loop');
        break;
      }
    }

    debugPrint('[HABIT_DETAILS_SCREEN] Generated ${weeks.length} proper weeks');
    return weeks;
  }

  List<List<DateTime>> _generateWeeksForMonth(
    DateTime monthStart,
    DateTime monthEnd,
  ) {
    final weeks = <List<DateTime>>[];

    // Start from the first day of the week containing the first day of the month
    DateTime weekStart = monthStart.subtract(
      Duration(days: monthStart.weekday % 7),
    );

    while (weekStart.isBefore(monthEnd) || weekStart.month == monthEnd.month) {
      final week = <DateTime>[];
      for (int i = 0; i < 7; i++) {
        week.add(weekStart.add(Duration(days: i)));
      }
      weeks.add(week);
      weekStart = weekStart.add(const Duration(days: 7));
    }

    return weeks;
  }

  Map<DateTime, bool> _getCompletionsMap() {
    final completions = <DateTime, bool>{};

    // Use heatmapData if provided, otherwise fall back to entries
    if (widget.heatmapData != null) {
      // Use the provided heatmap data for better performance and consistency
      for (final date in widget.heatmapData!) {
        completions[date] = true;
      }
      debugPrint(
        '[HABIT_DETAILS_SCREEN] Using provided heatmap data: ${widget.heatmapData!.length} completion dates',
      );
    } else {
      // Fallback to processing entries directly
      for (final entry in widget.entries) {
        final date = DateTime(
          entry.timestamp.year,
          entry.timestamp.month,
          entry.timestamp.day,
        );
        completions[date] = _isEntryCompleted(entry);
      }
      debugPrint(
        '[HABIT_DETAILS_SCREEN] Generated completions from entries: ${completions.length} dates',
      );
    }

    return completions;
  }

  bool _isEntryCompleted(Entry entry) {
    if (entry.type == EntryType.boolean) {
      return entry.boolValue;
    } else if (entry.type == EntryType.numerical && widget.habit.targetValue != null) {
      final value = entry.numericalValue ?? 0.0;
      return value >= (widget.habit.targetValue! * 0.8);
    }
    return false;
  }

  // TASK 3: Enhanced completion highlighting with habit's primary color
  Color _getEnhancedHeatmapColor(
    ThemeData theme,
    bool isCompleted,
    bool isCurrentMonth,
  ) {
    debugPrint('[HABIT_DETAILS_SCREEN] === GETTING ENHANCED HEATMAP COLOR ===');
    debugPrint(
      '[HABIT_DETAILS_SCREEN] Getting heatmap color - completed: $isCompleted, currentMonth: $isCurrentMonth',
    );

    if (!isCurrentMonth) {
      // Faded color for out-of-range dates
      return theme.colorScheme.surfaceContainerHighest.withOpacity(0.2);
    }

    if (isCompleted) {
      // TASK 3: Use habit's primary color for completed dates with full opacity
      final habitColor = theme.colorScheme.primary;
      debugPrint('[HABIT_DETAILS_SCREEN] Using completion color: $habitColor');
      return habitColor;
    } else {
      // TASK 3: Light neutral background for incomplete dates
      return theme.colorScheme.surface.withOpacity(0.3);
    }
  }

  // Keep original method for backward compatibility
  Color _getHeatmapColor(
    ThemeData theme,
    bool isCompleted,
    bool isCurrentMonth,
  ) {
    return _getEnhancedHeatmapColor(theme, isCompleted, isCurrentMonth);
  }

  // TASK 3: Build vertical weekday labels for heatmap (on the side)
  Widget _buildVerticalWeekdayLabelsForHeatmap(ThemeData theme) {
    debugPrint(
      '[HABIT_DETAILS_SCREEN] === BUILDING VERTICAL WEEKDAY LABELS FOR HEATMAP ===',
    );
    const weekdays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: weekdays
          .map(
            (day) => Container(
              width: 40, // Wider to accommodate full day names
              height: 22, // Match heatmap cell height
              margin: EdgeInsets.only(bottom: 2),
              child: Align(
                alignment: Alignment.centerRight,
                child: Text(
                  day, // TASK 3: Show full day name (not just first letter)
                  style: GoogleFonts.inter(
                    fontSize: (9 * 0.95),
                    fontWeight: FontWeight.w500,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ),
            ),
          )
          .toList(),
    );
  }



  // TASK 3: Build month headers for week structure (adjusted for side weekday labels)
  Widget _buildMonthHeadersForWeeks(
    ThemeData theme,
    List<List<DateTime?>> weeks,
  ) {
    debugPrint(
      '[HABIT_DETAILS_SCREEN] === BUILDING MONTH HEADERS FOR WEEKS ===',
    );
    debugPrint('[HABIT_DETAILS_SCREEN] Total weeks: ${weeks.length}');

    final monthHeaders = <Widget>[];
    String? currentMonth;
    int weekCount = 0;

    // TASK 3: Add spacing for weekday labels on the left
    monthHeaders.add(SizedBox(width: 50)); // Space for weekday labels

    for (int i = 0; i < weeks.length; i++) {
      final week = weeks[i];
      final firstDate = week.firstWhere(
        (date) => date != null,
        orElse: () => null,
      );

      if (firstDate != null) {
        final monthName = _getShortMonthName(firstDate);

        if (currentMonth != monthName) {
          if (currentMonth != null && weekCount > 0) {
            monthHeaders.add(
              SizedBox(
                width: weekCount * 24.0,
                child: Text(
                  currentMonth,
                  textAlign: TextAlign.center,
                  style: GoogleFonts.inter(
                    fontSize: (10 * 0.95),
                    fontWeight: FontWeight.w500,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ),
            );
            debugPrint(
              '[HABIT_DETAILS_SCREEN] Added month header: $currentMonth (width: ${weekCount * 24.0})',
            );
          }
          currentMonth = monthName;
          weekCount = 1;
        } else {
          weekCount++;
        }
      }
    }

    // Add final month header
    if (currentMonth != null && weekCount > 0) {
      monthHeaders.add(
        SizedBox(
          width: weekCount * 24.0,
          child: Text(
            currentMonth,
            textAlign: TextAlign.center,
            style: GoogleFonts.inter(
              fontSize: (10 * 0.95),
              fontWeight: FontWeight.w500,
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ),
      );
      debugPrint(
        '[HABIT_DETAILS_SCREEN] Added final month header: $currentMonth (width: ${weekCount * 24.0})',
      );
    }

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: monthHeaders,
    );
  }

  String _getMonthName(DateTime date) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    return '${months[date.month - 1]} ${date.year}';
  }

  // TASK 3: Get short month name for horizontal headers
  String _getShortMonthName(DateTime date) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    return months[date.month - 1];
  }

}

/// Custom painter for Y-axis
class _YAxisPainter extends CustomPainter {
  final ThemeData theme;
  final double minValue;
  final double maxValue;
  final List<double> intervals;

  _YAxisPainter({
    required this.theme,
    required this.minValue,
    required this.maxValue,
    required this.intervals,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = theme.colorScheme.outline.withOpacity(0.3)
      ..strokeWidth = 1;

    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
    );

    for (final value in intervals) {
      final y = size.height - (value / (maxValue - minValue)) * size.height;
      
      // Draw tick mark
      canvas.drawLine(
        Offset(size.width - 5, y),
        Offset(size.width, y),
        paint,
      );

      // Draw label
      textPainter.text = TextSpan(
        text: value.toStringAsFixed(1),
        style: GoogleFonts.inter(
          fontSize: 10,
          color: theme.colorScheme.onSurface.withOpacity(0.6),
        ),
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(size.width - textPainter.width - 8, y - textPainter.height / 2),
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
