import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'dart:math' as math;
import 'habit_analytics_service.dart';
import 'habit.dart';
import 'section.dart';

/// Structured Timeline Chart - Matches 52.jpg design requirements
/// Features:
/// - Y-axis with 6 points: 0%, 20%, 40%, 60%, 80%, 100%
/// - Always exactly 14 data points on X-axis
/// - Scrollable timeline with defined end (current date + 2-period buffer)
/// - Visual gaps for 0% scores (no line drawn to/from those points)
/// - Auto-positions to show most recent data
class StructuredTimelineChart extends StatefulWidget {
  final List<ChartDataPoint> dataPoints;
  final ThemeData theme;
  final TimeScale timeScale;
  final Color? habitColor;
  final double chartHeight;

  const StructuredTimelineChart({
    super.key,
    required this.dataPoints,
    required this.theme,
    required this.timeScale,
    this.habitColor,
    this.chartHeight = 250.0,
  });

  @override
  State<StructuredTimelineChart> createState() =>
      _StructuredTimelineChartState();
}

class _StructuredTimelineChartState extends State<StructuredTimelineChart> {
  late PageController _pageController;
  static const int pointsPerView = 14; // Always show exactly 14 points
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();

    // Auto-scroll to show recent data after build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToRecentData();
    });
  }

  void _scrollToRecentData() {
    if (_pageController.hasClients && widget.dataPoints.isNotEmpty) {
      // Calculate the page that contains the most recent data
      final totalPages = _calculateTotalPages();
      if (totalPages > 0) {
        final targetPage = totalPages - 1; // Most recent page
        _pageController.animateToPage(
          targetPage,
          duration: const Duration(milliseconds: 800),
          curve: Curves.easeOutCubic,
        );
      }
    }
  }

  int _calculateTotalPages() {
    if (widget.dataPoints.isEmpty) return 1;
    return (widget.dataPoints.length / pointsPerView).ceil();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final totalPages = _calculateTotalPages();

    return Column(
      children: [
        Expanded(
          child: PageView.builder(
            controller: _pageController,
            itemCount: totalPages,
            scrollDirection: Axis.horizontal,
            physics: const ClampingScrollPhysics(),
            onPageChanged: (page) {
              setState(() {
                _currentPage = page;
              });
            },
            itemBuilder: (context, pageIndex) {
              // Get exactly 14 data points for this page
              final startIndex = pageIndex * pointsPerView;
              final endIndex = math.min(
                startIndex + pointsPerView,
                widget.dataPoints.length,
              );
              final pageDataPoints = widget.dataPoints.sublist(
                startIndex,
                endIndex,
              );

              // Pad with empty data points if needed to always show 14 points
              while (pageDataPoints.length < pointsPerView) {
                pageDataPoints.add(
                  ChartDataPoint(
                    date: DateTime.now().add(
                      Duration(days: pageDataPoints.length),
                    ),
                    value: -1.0, // No data
                    label: '',
                    xValue: pageDataPoints.length.toDouble(),
                  ),
                );
              }

              return LayoutBuilder(
                builder: (context, constraints) {
                  // Debug logging
                  print(
                    'Page $pageIndex: ${pageDataPoints.length} data points',
                  );
                  print(
                    'Container size: ${constraints.maxWidth} x ${constraints.maxHeight}',
                  );

                  return CustomPaint(
                    painter: StructuredTimelineChartPainter(
                      dataPoints: pageDataPoints,
                      theme: widget.theme,
                      timeScale: widget.timeScale,
                      habitColor: widget.habitColor,
                      chartHeight: widget.chartHeight,
                      containerWidth: constraints.maxWidth,
                      containerHeight: constraints.maxHeight,
                    ),
                    size: Size(constraints.maxWidth, constraints.maxHeight),
                  );
                },
              );
            },
          ),
        ),
        // Page indicator
        if (totalPages > 1)
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'Page ${_currentPage + 1} of $totalPages',
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    color: widget.theme.colorScheme.onSurfaceVariant,
                  ),
                ),
                const SizedBox(width: 16),
                Row(
                  children: List.generate(totalPages, (index) {
                    return Container(
                      margin: const EdgeInsets.symmetric(horizontal: 2),
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: index == _currentPage
                            ? widget.theme.colorScheme.primary
                            : widget.theme.colorScheme.outline.withOpacity(0.3),
                      ),
                    );
                  }),
                ),
              ],
            ),
          ),
      ],
    );
  }
}

/// Custom painter for the structured timeline chart
class StructuredTimelineChartPainter extends CustomPainter {
  final List<ChartDataPoint> dataPoints;
  final ThemeData theme;
  final TimeScale timeScale;
  final Color? habitColor;
  final double chartHeight;
  final double containerWidth;
  final double containerHeight;

  StructuredTimelineChartPainter({
    required this.dataPoints,
    required this.theme,
    required this.timeScale,
    this.habitColor,
    required this.chartHeight,
    required this.containerWidth,
    required this.containerHeight,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (dataPoints.isEmpty) return;

    // Define chart boundaries
    const double topMargin = 30.0;
    const double bottomMargin = 60.0;
    const double leftMargin = 60.0;
    const double rightMargin = 20.0;

    final double chartAreaHeight = containerHeight - topMargin - bottomMargin;
    final double chartAreaWidth = containerWidth - leftMargin - rightMargin;

    // Use habit color if provided, otherwise fall back to theme primary
    final chartColor = habitColor ?? theme.colorScheme.primary;

    // Paint objects
    final gridPaint = Paint()
      ..color = theme.colorScheme.outline.withOpacity(0.2)
      ..strokeWidth = 1.0;

    final linePaint = Paint()
      ..color = chartColor
      ..strokeWidth = 3.0
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    final dotPaint = Paint()
      ..color = chartColor
      ..style = PaintingStyle.fill;

    final emptyDotPaint = Paint()
      ..color = theme.colorScheme.outline.withOpacity(0.3)
      ..style = PaintingStyle.fill;

    final textPainter = TextPainter(textDirection: TextDirection.ltr);

    // Draw Y-axis with 6 distinct points: 0%, 20%, 40%, 60%, 80%, 100%
    _drawYAxisGridAndLabels(
      canvas,
      textPainter,
      leftMargin,
      topMargin,
      chartAreaHeight,
      gridPaint,
    );

    // Draw data points and lines with visual gaps for 0% scores
    _drawDataPointsAndLines(
      canvas,
      textPainter,
      leftMargin,
      topMargin,
      chartAreaHeight,
      linePaint,
      dotPaint,
      emptyDotPaint,
      chartColor,
    );

    // Draw X-axis labels
    _drawXAxisLabels(
      canvas,
      textPainter,
      leftMargin,
      topMargin + chartAreaHeight + 15,
    );
  }

  /// Draw Y-axis with exactly 6 points: 0%, 20%, 40%, 60%, 80%, 100%
  void _drawYAxisGridAndLabels(
    Canvas canvas,
    TextPainter textPainter,
    double leftMargin,
    double topMargin,
    double chartAreaHeight,
    Paint gridPaint,
  ) {
    final percentages = [0, 20, 40, 60, 80, 100];

    for (int i = 0; i < percentages.length; i++) {
      final percentage = percentages[i];
      final y =
          topMargin + chartAreaHeight - (chartAreaHeight * percentage / 100);

      // Draw horizontal grid line spanning full width
      canvas.drawLine(
        Offset(leftMargin, y),
        Offset(
          containerWidth - 20, // rightMargin
          y,
        ), // Extend across full available width
        gridPaint,
      );

      // Draw Y-axis percentage label
      textPainter.text = TextSpan(
        text: '$percentage%',
        style: GoogleFonts.inter(
          fontSize: 12,
          color: theme.colorScheme.onSurfaceVariant,
          fontWeight: FontWeight.w600,
        ),
      );
      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(leftMargin - textPainter.width - 10, y - textPainter.height / 2),
      );
    }

    // Add small padding above 100% line
    final paddingY = topMargin - 10;
    canvas.drawLine(
      Offset(leftMargin, paddingY),
      Offset(leftMargin + chartAreaHeight * 3, paddingY),
      Paint()
        ..color = theme.colorScheme.outline.withOpacity(0.1)
        ..strokeWidth = 0.5,
    );
  }

  /// Draw data points and connecting lines with visual gaps for 0% scores
  void _drawDataPointsAndLines(
    Canvas canvas,
    TextPainter textPainter,
    double leftMargin,
    double topMargin,
    double chartAreaHeight,
    Paint linePaint,
    Paint dotPaint,
    Paint emptyDotPaint,
    Color chartColor,
  ) {
    final List<Offset> validPoints = [];

    // Ensure we always draw exactly 14 points across the full width
    final chartAreaWidth =
        containerWidth - leftMargin - 20; // Account for margins
    final actualPointSpacing = chartAreaWidth / 13; // 14 points = 13 intervals

    for (int i = 0; i < 14; i++) {
      // Always draw exactly 14 points
      final x = leftMargin + (i * actualPointSpacing);

      // Get data point if available, otherwise treat as no data
      final point = i < dataPoints.length ? dataPoints[i] : null;
      final value = point?.value ?? -1.0;

      if (value == -1.0) {
        // No data - draw empty indicator and break line
        final emptyY = topMargin + chartAreaHeight / 2;
        canvas.drawCircle(Offset(x, emptyY), 4.0, emptyDotPaint);

        // Draw line segment if we have valid points, then clear for gap
        if (validPoints.isNotEmpty) {
          _drawLineSegment(canvas, validPoints, linePaint);
          validPoints.clear();
        }
      } else if (value == 0.0) {
        // 0% score - create visual gap (no line to/from this point)
        final y = topMargin + chartAreaHeight; // Bottom of chart (0%)

        // Draw the dot but don't connect lines
        canvas.drawCircle(Offset(x, y), 5.0, emptyDotPaint);

        // Draw line segment if we have valid points, then clear for gap
        if (validPoints.isNotEmpty) {
          _drawLineSegment(canvas, validPoints, linePaint);
          validPoints.clear();
        }
      } else {
        // Valid data point with score > 0%
        final normalizedValue = value.clamp(0.0, 100.0);
        final y =
            topMargin +
            chartAreaHeight -
            (chartAreaHeight * normalizedValue / 100);

        validPoints.add(Offset(x, y));

        // Draw data point dot
        canvas.drawCircle(Offset(x, y), 6.0, dotPaint);

        // Draw white border around dot for better visibility
        canvas.drawCircle(
          Offset(x, y),
          6.0,
          Paint()
            ..color = theme.colorScheme.surface
            ..strokeWidth = 2.0
            ..style = PaintingStyle.stroke,
        );
      }
    }

    // Draw final line segment if any points remain
    if (validPoints.isNotEmpty) {
      _drawLineSegment(canvas, validPoints, linePaint);
    }
  }

  /// Draw a line segment connecting valid points
  void _drawLineSegment(Canvas canvas, List<Offset> points, Paint linePaint) {
    if (points.length < 2) return;

    final path = Path();
    path.moveTo(points.first.dx, points.first.dy);

    for (int i = 1; i < points.length; i++) {
      path.lineTo(points[i].dx, points[i].dy);
    }

    canvas.drawPath(path, linePaint);
  }

  /// Draw X-axis labels with smart formatting
  void _drawXAxisLabels(
    Canvas canvas,
    TextPainter textPainter,
    double leftMargin,
    double labelY,
  ) {
    // Ensure we always draw exactly 14 labels across the full width
    final chartAreaWidth =
        containerWidth - leftMargin - 20; // Account for margins
    final actualPointSpacing = chartAreaWidth / 13; // 14 points = 13 intervals

    for (int i = 0; i < 14; i++) {
      // Always draw exactly 14 labels
      final x = leftMargin + (i * actualPointSpacing);

      // Get data point if available
      final point = i < dataPoints.length ? dataPoints[i] : null;
      if (point == null) continue; // Skip if no data point

      String label;
      bool isSpecialLabel = false;

      // Smart label formatting based on time scale
      if (timeScale == TimeScale.day) {
        if (point.date.day == 1) {
          // Show month name for first day of month
          const monthNames = [
            'Jan',
            'Feb',
            'Mar',
            'Apr',
            'May',
            'Jun',
            'Jul',
            'Aug',
            'Sep',
            'Oct',
            'Nov',
            'Dec',
          ];
          label = monthNames[point.date.month - 1];
          isSpecialLabel = true;
        } else {
          // Show day number
          label = point.date.day.toString();
        }
      } else if (timeScale == TimeScale.month) {
        if (point.date.month == 1) {
          // Show year for January
          label = point.date.year.toString();
          isSpecialLabel = true;
        } else {
          // Show month name
          const monthNames = [
            'Jan',
            'Feb',
            'Mar',
            'Apr',
            'May',
            'Jun',
            'Jul',
            'Aug',
            'Sep',
            'Oct',
            'Nov',
            'Dec',
          ];
          label = monthNames[point.date.month - 1];
        }
      } else {
        // Use the provided label for other time scales
        label = point.label;
      }

      textPainter.text = TextSpan(
        text: label,
        style: GoogleFonts.inter(
          fontSize: isSpecialLabel ? 12 : 11,
          color: theme.colorScheme.onSurfaceVariant,
          fontWeight: isSpecialLabel ? FontWeight.w600 : FontWeight.normal,
        ),
      );
      textPainter.layout();
      textPainter.paint(canvas, Offset(x - textPainter.width / 2, labelY));

      // Draw vertical transition lines for special labels
      if (isSpecialLabel) {
        canvas.drawLine(
          Offset(x, 30),
          Offset(x, chartHeight - 40),
          Paint()
            ..color = theme.colorScheme.primary.withOpacity(0.3)
            ..strokeWidth = 1.5,
        );
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// Helper function to get habit color based on section assignment
Color getStructuredHabitColor(Habit habit, List<Section> sections) {
  // Get color from section assignment
  if (habit.sectionIds.isNotEmpty && sections.isNotEmpty) {
    final section = sections.firstWhere(
      (s) => s.id == habit.sectionIds.first,
      orElse: () => sections.first,
    );

    // Convert section color hex to Color object
    return _hexToColor(section.color);
  }

  // Fallback to hash-based color generation if no section
  final colors = [
    const Color(0xFF4F46E5), // indigo
    const Color(0xFF059669), // emerald
    const Color(0xFFDC2626), // red
    const Color(0xFFCA8A04), // yellow
    const Color(0xFF7C3AED), // violet
    const Color(0xFF0891B2), // cyan
  ];
  return colors[habit.name.hashCode % colors.length];
}

/// Convert hex string to Color object
Color _hexToColor(String hex) {
  try {
    // Remove # if present
    final cleanHex = hex.replaceAll('#', '');
    final colorValue = int.parse(cleanHex, radix: 16);
    return Color(0xFF000000 + colorValue);
  } catch (e) {
    // Fallback to blue if parsing fails
    return const Color(0xFF3B82F6);
  }
}
