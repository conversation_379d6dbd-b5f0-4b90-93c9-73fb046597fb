import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'dart:math' as math;
import 'habit_analytics_service.dart';
import 'habit.dart';
import 'section.dart';

/// WORKING TIMELINE CHART - Complete functional replacement
class WorkingTimelineChart extends StatefulWidget {
  final List<ChartDataPoint> dataPoints;
  final ThemeData theme;
  final TimeScale timeScale;
  final double pointSpacing;
  final double chartHeight;
  final double containerWidth;
  final Color? habitColor; // NEW: Add habit color parameter

  const WorkingTimelineChart({
    super.key,
    required this.dataPoints,
    required this.theme,
    required this.timeScale,
    required this.pointSpacing,
    required this.chartHeight,
    required this.containerWidth,
    this.habitColor, // NEW: Optional habit color
  });

  @override
  State<WorkingTimelineChart> createState() => _WorkingTimelineChartState();
}

class _WorkingTimelineChartState extends State<WorkingTimelineChart> {
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    
    // Auto-scroll to show recent data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToRecentData();
    });
  }

  void _scrollToRecentData() {
    if (_scrollController.hasClients && widget.dataPoints.isNotEmpty) {
      final maxScroll = _scrollController.position.maxScrollExtent;
      if (maxScroll > 0) {
        // Position to show current data with 2-period buffer
        final targetScroll = maxScroll - (2 * widget.pointSpacing);
        _scrollController.animateTo(
          targetScroll.clamp(0.0, maxScroll),
          duration: const Duration(milliseconds: 800),
          curve: Curves.easeOutCubic,
        );
      }
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      controller: _scrollController,
      scrollDirection: Axis.horizontal,
      physics: const ClampingScrollPhysics(), // Prevents over-scroll into future
      child: Container(
        width: widget.containerWidth,
        height: widget.chartHeight + 50,
        child: CustomPaint(
          painter: WorkingTimelineChartPainter(
            dataPoints: widget.dataPoints,
            theme: widget.theme,
            timeScale: widget.timeScale,
            pointSpacing: widget.pointSpacing,
            chartHeight: widget.chartHeight,
            habitColor: widget.habitColor, // NEW: Pass habit color to painter
          ),
          size: Size(widget.containerWidth, widget.chartHeight + 50),
        ),
      ),
    );
  }
}

/// Robust Timeline Chart Widget - Complete replacement for broken Score Chart
class _RobustTimelineChart extends StatefulWidget {
  final List<ChartDataPoint> dataPoints;
  final ThemeData theme;
  final TimeScale timeScale;
  final double pointSpacing;
  final double chartHeight;
  final Function(ScrollController)? onBuild;

  const _RobustTimelineChart({
    required this.dataPoints,
    required this.theme,
    required this.timeScale,
    required this.pointSpacing,
    required this.chartHeight,
    this.onBuild,
  });

  @override
  State<_RobustTimelineChart> createState() => _RobustTimelineChartState();
}

class _RobustTimelineChartState extends State<_RobustTimelineChart> {
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    widget.onBuild?.call(_scrollController);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: _RobustTimelineChartPainter(
        dataPoints: widget.dataPoints,
        theme: widget.theme,
        timeScale: widget.timeScale,
        pointSpacing: widget.pointSpacing,
        chartHeight: widget.chartHeight,
      ),
      size: Size.infinite,
    );
  }
}

/// WORKING TIMELINE CHART PAINTER - Functional implementation
class WorkingTimelineChartPainter extends CustomPainter {
  final List<ChartDataPoint> dataPoints;
  final ThemeData theme;
  final TimeScale timeScale;
  final double pointSpacing;
  final double chartHeight;
  final Color? habitColor; // NEW: Add habit color for theming

  WorkingTimelineChartPainter({
    required this.dataPoints,
    required this.theme,
    required this.timeScale,
    required this.pointSpacing,
    required this.chartHeight,
    this.habitColor, // NEW: Optional habit color
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (dataPoints.isEmpty) return;

    // Define chart boundaries
    const double topMargin = 20.0;
    const double bottomMargin = 50.0;
    const double leftMargin = 60.0;
    const double rightMargin = 20.0;
    
    final double chartAreaHeight = chartHeight - topMargin - bottomMargin;
    final double chartAreaWidth = size.width - leftMargin - rightMargin;

    // Use habit color if provided, otherwise fall back to theme primary
    final chartColor = habitColor ?? theme.colorScheme.primary;

    // Paint objects with enhanced styling
    final linePaint = Paint()
      ..color = chartColor
      ..strokeWidth = 3.0
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    final dotPaint = Paint()
      ..color = chartColor
      ..style = PaintingStyle.fill;

    final emptyDotPaint = Paint()
      ..color = theme.colorScheme.outline.withOpacity(0.5)
      ..style = PaintingStyle.fill;

    // Simplified grid paint for cleaner look
    final gridPaint = Paint()
      ..color = theme.colorScheme.outline.withOpacity(0.15)
      ..strokeWidth = 1.0;

    final textPainter = TextPainter(textDirection: TextDirection.ltr);

    // Draw simplified Y-axis grid and labels (0%, 50%, 100% only)
    _drawSimplifiedYAxisGridAndLabels(canvas, textPainter, leftMargin, topMargin, chartAreaHeight, gridPaint);

    // Process data points and draw chart with gradient fill and smooth curves
    _drawEnhancedDataPointsAndLines(canvas, textPainter, leftMargin, topMargin, chartAreaHeight, linePaint, dotPaint, emptyDotPaint, chartColor);

    // Draw X-axis labels
    _drawXAxisLabels(canvas, textPainter, leftMargin, topMargin + chartAreaHeight + 10);
  }

  // NEW: Simplified Y-axis with only 0%, 50%, 100%
  void _drawSimplifiedYAxisGridAndLabels(Canvas canvas, TextPainter textPainter, double leftMargin, double topMargin, double chartAreaHeight, Paint gridPaint) {
    final percentages = [0, 50, 100];
    
    for (int i = 0; i < percentages.length; i++) {
      final percentage = percentages[i];
      final y = topMargin + chartAreaHeight - (chartAreaHeight * percentage / 100);
      
      // Draw horizontal grid line
      canvas.drawLine(
        Offset(leftMargin, y),
        Offset(leftMargin + chartAreaHeight * 2, y), // Extend across chart area
        gridPaint,
      );
      
      // Draw Y-axis percentage label
      textPainter.text = TextSpan(
        text: '$percentage%',
        style: GoogleFonts.inter(
          fontSize: 12,
          color: theme.colorScheme.onSurfaceVariant,
          fontWeight: FontWeight.w500,
        ),
      );
      textPainter.layout();
      textPainter.paint(
        canvas, 
        Offset(leftMargin - textPainter.width - 10, y - textPainter.height / 2),
      );
    }
  }

  // NEW: Enhanced data points with smooth curves, gradient fill, and improved indicators
  void _drawEnhancedDataPointsAndLines(Canvas canvas, TextPainter textPainter, double leftMargin, double topMargin, double chartAreaHeight, Paint linePaint, Paint dotPaint, Paint emptyDotPaint, Color chartColor) {
    final List<Offset> validPoints = [];
    
    for (int i = 0; i < dataPoints.length; i++) {
      final point = dataPoints[i];
      final x = leftMargin + (i * pointSpacing) + (pointSpacing / 2);
      
      if (point.value == -1.0) {
        // No data - draw empty indicator and break line
        final emptyY = topMargin + chartAreaHeight / 2;
        canvas.drawCircle(Offset(x, emptyY), 4.0, emptyDotPaint);
        
        // Draw line segment with gradient if we have valid points
        if (validPoints.isNotEmpty) {
          _drawSmoothLineWithGradient(canvas, validPoints, linePaint, chartColor, topMargin, chartAreaHeight);
          validPoints.clear();
        }
      } else {
        // Valid data point
        final normalizedValue = point.value.clamp(0.0, 100.0);
        final y = topMargin + chartAreaHeight - (chartAreaHeight * normalizedValue / 100);
        
        validPoints.add(Offset(x, y));
        
        // Draw larger data point dot (6.0 instead of 5.0)
        canvas.drawCircle(Offset(x, y), 6.0, dotPaint);
        
        // Draw white border around dot
        canvas.drawCircle(
          Offset(x, y),
          6.0,
          Paint()
            ..color = theme.colorScheme.surface
            ..strokeWidth = 2.0
            ..style = PaintingStyle.stroke,
        );
        
        // Add pulse animation for the most recent data point
        if (i == dataPoints.length - 1) {
          _drawPulsingIndicator(canvas, Offset(x, y), chartColor);
        }
      }
    }

    // Draw final line segment with gradient if any points remain
    if (validPoints.isNotEmpty) {
      _drawSmoothLineWithGradient(canvas, validPoints, linePaint, chartColor, topMargin, chartAreaHeight);
    }
  }

  // NEW: Draw smooth curved line with gradient fill
  void _drawSmoothLineWithGradient(Canvas canvas, List<Offset> points, Paint linePaint, Color chartColor, double topMargin, double chartAreaHeight) {
    if (points.length < 2) return;
    
    // Create smooth curve path
    final path = Path();
    path.moveTo(points.first.dx, points.first.dy);
    
    if (points.length == 2) {
      // Simple line for two points
      path.lineTo(points.last.dx, points.last.dy);
    } else {
      // Create smooth curves using quadratic bezier curves
      for (int i = 1; i < points.length; i++) {
        final current = points[i];
        final previous = points[i - 1];
        
        if (i == points.length - 1) {
          // Last point - simple line
          path.lineTo(current.dx, current.dy);
        } else {
          // Create control point for smooth curve
          final next = points[i + 1];
          final controlX = current.dx;
          final controlY = current.dy;
          final endX = (current.dx + next.dx) / 2;
          final endY = (current.dy + next.dy) / 2;
          
          path.quadraticBezierTo(controlX, controlY, endX, endY);
        }
      }
    }
    
    // Draw the curved line
    canvas.drawPath(path, linePaint);
    
    // Create gradient fill area
    final fillPath = Path.from(path);
    // Close the path by going to bottom-right, then bottom-left, then back to start
    fillPath.lineTo(points.last.dx, topMargin + chartAreaHeight);
    fillPath.lineTo(points.first.dx, topMargin + chartAreaHeight);
    fillPath.close();
    
    // Create gradient paint
    final gradientPaint = Paint()
      ..shader = LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [
          chartColor.withOpacity(0.3),
          chartColor.withOpacity(0.1),
          chartColor.withOpacity(0.0),
        ],
        stops: const [0.0, 0.7, 1.0],
      ).createShader(Rect.fromLTWH(
        points.first.dx,
        topMargin,
        points.last.dx - points.first.dx,
        chartAreaHeight,
      ));
    
    // Draw gradient fill
    canvas.drawPath(fillPath, gradientPaint);
  }
  
  // NEW: Draw pulsing indicator for most recent data point
  void _drawPulsingIndicator(Canvas canvas, Offset center, Color color) {
    // Draw outer glow ring
    final glowPaint = Paint()
      ..color = color.withOpacity(0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;
    
    canvas.drawCircle(center, 10.0, glowPaint);
    
    // Draw inner glow ring
    final innerGlowPaint = Paint()
      ..color = color.withOpacity(0.5)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;
    
    canvas.drawCircle(center, 8.0, innerGlowPaint);
  }

  void _drawXAxisLabels(Canvas canvas, TextPainter textPainter, double leftMargin, double labelY) {
    for (int i = 0; i < dataPoints.length; i++) {
      final point = dataPoints[i];
      final x = leftMargin + (i * pointSpacing) + (pointSpacing / 2);
      
      String label;
      bool isSpecialLabel = false;
      
      // Smart label formatting based on requirements
      if (timeScale == TimeScale.day) {
        if (point.date.day == 1) {
          // Show month name for first day of month (e.g., "Jul")
          const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                             'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
          label = monthNames[point.date.month - 1];
          isSpecialLabel = true;
        } else {
          // Show day number (e.g., "30", "1", "2")
          label = point.date.day.toString();
        }
      } else if (timeScale == TimeScale.month) {
        if (point.date.month == 1) {
          // Show year for January (e.g., "2025")
          label = point.date.year.toString();
          isSpecialLabel = true;
        } else {
          // Show month name (e.g., "Dec", "Jan")
          const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                             'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
          label = monthNames[point.date.month - 1];
        }
      } else {
        // Use the provided label for other time scales
        label = point.label;
      }

      textPainter.text = TextSpan(
        text: label,
        style: GoogleFonts.inter(
          fontSize: isSpecialLabel ? 12 : 11,
          color: theme.colorScheme.onSurfaceVariant,
          fontWeight: isSpecialLabel ? FontWeight.w600 : FontWeight.normal,
        ),
      );
      textPainter.layout();
      textPainter.paint(canvas, Offset(x - textPainter.width / 2, labelY));
      
      // Draw vertical transition lines for special labels
      if (isSpecialLabel) {
        canvas.drawLine(
          Offset(x, 20),
          Offset(x, chartHeight - 30),
          Paint()
            ..color = theme.colorScheme.primary.withOpacity(0.3)
            ..strokeWidth = 1.5,
        );
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// Helper function to get habit color based on section assignment
Color getHabitColor(Habit habit, List<Section> sections) {
  // Get color from section assignment
  if (habit.sectionIds.isNotEmpty && sections.isNotEmpty) {
    final section = sections.firstWhere(
      (s) => s.id == habit.sectionIds.first,
      orElse: () => sections.first,
    );
    
    // Convert section color hex to Color object
    return _hexToColor(section.color);
  }
  
  // Fallback to hash-based color generation if no section
  final colors = [
    const Color(0xFF4F46E5), // indigo
    const Color(0xFF059669), // emerald
    const Color(0xFFDC2626), // red
    const Color(0xFFCA8A04), // yellow
    const Color(0xFF7C3AED), // violet
    const Color(0xFF0891B2), // cyan
  ];
  return colors[habit.name.hashCode % colors.length];
}

/// Convert hex string to Color object
Color _hexToColor(String hex) {
  try {
    // Remove # if present
    final cleanHex = hex.replaceAll('#', '');
    final colorValue = int.parse(cleanHex, radix: 16);
    return Color(0xFF000000 + colorValue);
  } catch (e) {
    // Fallback to blue if parsing fails
    return const Color(0xFF3B82F6);
  }
}

/// Custom painter for the robust timeline chart
class _RobustTimelineChartPainter extends CustomPainter {
  final List<ChartDataPoint> dataPoints;
  final ThemeData theme;
  final TimeScale timeScale;
  final double pointSpacing;
  final double chartHeight;

  _RobustTimelineChartPainter({
    required this.dataPoints,
    required this.theme,
    required this.timeScale,
    required this.pointSpacing,
    required this.chartHeight,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (dataPoints.isEmpty) return;

    // Define chart area
    const double topPadding = 20.0;
    const double bottomPadding = 35.0;
    const double leftPadding = 50.0;
    final double usableHeight = chartHeight - topPadding - bottomPadding;
    final double usableWidth = size.width - leftPadding;

    // Paint objects
    final linePaint = Paint()
      ..color = theme.colorScheme.primary
      ..strokeWidth = 2.5
      ..style = PaintingStyle.stroke;

    final dotPaint = Paint()
      ..color = theme.colorScheme.primary
      ..style = PaintingStyle.fill;

    final emptyDotPaint = Paint()
      ..color = theme.colorScheme.outline.withOpacity(0.4)
      ..style = PaintingStyle.fill;

    final gridPaint = Paint()
      ..color = theme.colorScheme.outline.withOpacity(0.15)
      ..strokeWidth = 1.0;

    final textPainter = TextPainter(textDirection: TextDirection.ltr);

    // Draw Y-axis grid lines and labels (0%, 25%, 50%, 75%, 100%)
    for (int i = 0; i <= 4; i++) {
      final percentage = i * 25;
      final y = topPadding + usableHeight - (usableHeight * i / 4);
      
      // Grid line
      canvas.drawLine(
        Offset(leftPadding, y),
        Offset(size.width, y),
        gridPaint,
      );
      
      // Y-axis label
      textPainter.text = TextSpan(
        text: '$percentage%',
        style: GoogleFonts.inter(
          fontSize: 11,
          color: theme.colorScheme.onSurfaceVariant,
          fontWeight: FontWeight.w500,
        ),
      );
      textPainter.layout();
      textPainter.paint(
        canvas, 
        Offset(leftPadding - textPainter.width - 8, y - textPainter.height / 2),
      );
    }

    // Draw data points and connecting lines
    final path = Path();
    bool pathStarted = false;
    final List<Offset> validPoints = [];

    for (int i = 0; i < dataPoints.length; i++) {
      final point = dataPoints[i];
      final x = leftPadding + (i * pointSpacing) + (pointSpacing / 2);
      
      if (point.value == -1.0) {
        // No data - draw empty indicator and break line
        final emptyY = topPadding + usableHeight / 2;
        canvas.drawCircle(Offset(x, emptyY), 3.0, emptyDotPaint);
        
        // Add gap in line
        if (validPoints.isNotEmpty) {
          _drawLinePath(canvas, validPoints, linePaint);
          validPoints.clear();
        }
        pathStarted = false;
      } else {
        // Valid data point
        final normalizedValue = point.value.clamp(0.0, 100.0);
        final y = topPadding + usableHeight - (usableHeight * normalizedValue / 100);
        
        validPoints.add(Offset(x, y));
        
        // Draw data point
        canvas.drawCircle(Offset(x, y), 4.0, dotPaint);
        
        // Draw border around dot
        canvas.drawCircle(
          Offset(x, y),
          4.0,
          Paint()
            ..color = theme.colorScheme.surface
            ..strokeWidth = 2.0
            ..style = PaintingStyle.stroke,
        );
      }

      // Draw X-axis labels
      _drawSmartXAxisLabel(canvas, textPainter, point, x, chartHeight + 5);
      
      // Draw vertical transition lines
      _drawVerticalTransitionLine(canvas, point, x, topPadding, usableHeight);
    }

    // Draw final line segment if any points remain
    if (validPoints.isNotEmpty) {
      _drawLinePath(canvas, validPoints, linePaint);
    }
  }

  void _drawLinePath(Canvas canvas, List<Offset> points, Paint paint) {
    if (points.length < 2) return;
    
    final path = Path();
    path.moveTo(points.first.dx, points.first.dy);
    
    for (int i = 1; i < points.length; i++) {
      path.lineTo(points[i].dx, points[i].dy);
    }
    
    canvas.drawPath(path, paint);
  }

  void _drawSmartXAxisLabel(Canvas canvas, TextPainter textPainter, ChartDataPoint point, double x, double y) {
    String label;
    bool isSpecialLabel = false;
    
    // Smart label formatting based on requirements
    if (timeScale == TimeScale.day) {
      if (point.date.day == 1) {
        // Show month name for first day of month (e.g., "Jul")
        const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                           'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        label = monthNames[point.date.month - 1];
        isSpecialLabel = true;
      } else {
        // Show day number (e.g., "30", "1", "2")
        label = point.date.day.toString();
      }
    } else if (timeScale == TimeScale.month) {
      if (point.date.month == 1) {
        // Show year for January (e.g., "2025")
        label = point.date.year.toString();
        isSpecialLabel = true;
      } else {
        // Show month name (e.g., "Dec", "Jan")
        const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                           'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        label = monthNames[point.date.month - 1];
      }
    } else {
      // Use the provided label for other time scales
      label = point.label;
    }

    textPainter.text = TextSpan(
      text: label,
      style: GoogleFonts.inter(
        fontSize: isSpecialLabel ? 11 : 10,
        color: theme.colorScheme.onSurfaceVariant,
        fontWeight: isSpecialLabel ? FontWeight.w600 : FontWeight.normal,
      ),
    );
    textPainter.layout();
    textPainter.paint(canvas, Offset(x - textPainter.width / 2, y));
  }

  void _drawVerticalTransitionLine(Canvas canvas, ChartDataPoint point, double x, double topPadding, double usableHeight) {
    bool shouldDrawLine = false;
    
    if (timeScale == TimeScale.day && point.date.day == 1) {
      shouldDrawLine = true; // First day of month
    } else if (timeScale == TimeScale.month && point.date.month == 1) {
      shouldDrawLine = true; // First month of year
    }

    if (shouldDrawLine) {
      canvas.drawLine(
        Offset(x, topPadding),
        Offset(x, topPadding + usableHeight),
        Paint()
          ..color = theme.colorScheme.primary.withOpacity(0.3)
          ..strokeWidth = 1.5,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate != this;
  }
}